export enum Condition {
  EXCELLENT = "Excellent",
  GOOD = "Good",
  FAIR = "Fair",
}

export type WebsitePrices = {
  sixMonths: number
  nineMonths: number
  twelveMonths: number
}

export type ReviewStats = {
  oneStar: number
  twoStar: number
  threeStar: number
  fourStar: number
  fiveStar: number
}

export type ProductType = {
  id: string
  name: string
  description: string
  heroImage: string
  category: string
  subcategory?: string
  images: string[]
  material: string
  size: string
  rating: number
  reviewCount: number
  reviewStats?: ReviewStats
  tags: string[]
  buyingPrice: number
  sellingPrice: number
  websitePrices: WebsitePrices
  condition: Condition
  showOnWebsite: boolean
  stockItems: number // Available items in stock
  rentedItems: number // Items currently rented out
  deliveryFee: number
  deliveryTime?: string
  createdAt: string
  updatedAt: string
  vendorId: string
}
