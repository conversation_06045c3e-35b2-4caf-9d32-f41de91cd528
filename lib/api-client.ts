// API client for making authenticated requests to the backend

// Define the base API error type
export class ApiError extends Error {
  status: number
  data?: any

  constructor(message: string, status: number, data?: any) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

// Define the base API response type
export interface ApiResponse<T> {
  data: T
  status: number
}

// Function to get the authentication token
// This will be replaced with the actual implementation from auth-context
let getTokenFunction: () => Promise<string | null> = async () => null

// Set the token getter function
export function setTokenGetter(fn: () => Promise<string | null>) {
  getTokenFunction = fn
}

// Base API client for making authenticated requests
export async function apiClient<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  // Get the authentication token
  const token = await getTokenFunction()
  
  // Check if the body is FormData to avoid setting Content-Type
  const isFormData = options.body instanceof FormData
  
  // Prepare headers with authentication
  const headers: HeadersInit = {
    // Only set Content-Type for non-FormData requests
    ...(isFormData ? {} : { 'Content-Type': 'application/json' }),
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options.headers,
  }
  // Prepare the request URL
  const url = `${process.env.NEXT_PUBLIC_BACKEND_URL}${endpoint}`
  
  try {
    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
    })

    // Handle non-JSON responses
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.indexOf('application/json') === -1) {
      if (!response.ok) {
        throw new ApiError('API request failed', response.status)
      }
      return { data: await response.text() as unknown as T, status: response.status }
    }

    // Parse the JSON response
    const data = await response.json()

    // Handle error responses
    if (!response.ok) {
      throw new ApiError(
        data.error || 'Something went wrong',
        response.status,
        data
      )
    }

    return { data, status: response.status }
  } catch (error) {
    // Handle fetch errors
    if (error instanceof ApiError) {
      throw error
    }

    // Handle network errors
    if (error instanceof Error) {
      throw new ApiError(error.message, 0)
    }

    // Handle unknown errors
    throw new ApiError('Unknown error', 0)
  }
}

// Convenience methods for common HTTP methods
export const api = {
  get: <T = any>(endpoint: string, options?: RequestInit) => 
    apiClient<T>(endpoint, { ...options, method: 'GET' }),
  
  post: <T = any>(endpoint: string, data?: any, options?: RequestInit) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  put: <T = any>(endpoint: string, data?: any, options?: RequestInit) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  patch: <T = any>(endpoint: string, data?: any, options?: RequestInit) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  delete: <T = any>(endpoint: string, options?: RequestInit) =>
    apiClient<T>(endpoint, { ...options, method: 'DELETE' }),

  // Special method for form data
  formData: <T = any>(endpoint: string, formData: FormData, options?: RequestInit) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, browser will set it with boundary
        ...options?.headers,
      },
    }),
}

// Vendor-specific API endpoints
export const vendorApi = {
  // Profile endpoints
  getProfile: () => api.get<any>('/api/vendor/profile'),
  updateProfile: (data: any) => api.put<any>('/api/vendor/profile', data),
  getProfileCompletion: () => api.get<any>('/api/vendor/profile-completion'),
  uploadProfilePhoto: (formData: FormData) => 
    api.formData<any>('/api/vendor/profile/upload-photo', formData),

  // Address endpoints
  getAddresses: () => api.get<any>('/api/vendor/addresses'),
  addAddress: (data: any) => api.post<any>('/api/vendor/addresses', data),
  updateAddress: (id: string, data: any) => 
    api.put<any>(`/api/vendor/addresses/${id}`, data),
  deleteAddress: (id: string) => 
    api.delete<any>(`/api/vendor/addresses/${id}`),

  // Banking endpoints
  getBankDetails: () => api.get<any>('/api/vendor/banking'),
  updateBankDetails: (data: any) => api.put<any>('/api/vendor/banking', data),

  // KYC endpoints
  getKycDetails: () => api.get<any>('/api/vendor/kyc'),
  submitKyc: (formData: FormData) => 
    api.formData<any>('/api/vendor/kyc', formData),

  // Product endpoints
  getProducts: () => api.get<any>('/api/vendor/products/get'),
  addProduct: (formData: FormData) => 
    api.formData<any>('/api/vendor/products/add', formData),
  updateProduct: (id: string, formData: FormData) => 
    api.formData<any>(`/api/vendor/products/update?id=${id}`, formData),
  deleteProduct: (id: string) => 
    api.delete<any>(`/api/vendor/products/delete?id=${id}`),

  // Order endpoints
  getOrders: () => api.get<any>('/api/vendor/orders'),
  getOrderDetails: (id: string) => 
    api.get<any>(`/api/vendor/orders/${id}`),
  updateOrderStatus: (id: string, status: string) => 
    api.put<any>(`/api/vendor/orders/${id}/status`, { status }),

  // Dashboard endpoints
  getDashboardData: (period: string) => 
    api.get<any>(`/api/vendor/dashboard?period=${period}`),

  // Check if vendor exists
  checkVendorExists: (data: { email?: string; uid?: string }) => 
    api.post<any>('/api/vendor/check-exists', data),
}
