// components/add-product-dialog.tsx
"use client";

import type React from "react";

import { useState, useRef } from "react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Sparkles, Check, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { GoogleGenAI } from "@google/genai";
import { auth } from "@/lib/firebase";

interface AddProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProductAdded?: () => void;
}

export function AddProductDialog({
  open,
  onOpenChange,
  onProductAdded,
}: AddProductDialogProps) {
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const additionalImagesInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: process.env.NODE_ENV === "development" ? "Modern Leather Sofa" : "",
    category: process.env.NODE_ENV === "development" ? "Living Room" : "",
    subcategory: process.env.NODE_ENV === "development" ? "Sofa" : "",
    buyingPrice: process.env.NODE_ENV === "development" ? "25000" : "",
    sellingPrice: process.env.NODE_ENV === "development" ? "35000" : "",
    websitePrices: {
      sixMonths: process.env.NODE_ENV === "development" ? "5249" : "",
      nineMonths: process.env.NODE_ENV === "development" ? "4669" : "",
      twelveMonths: process.env.NODE_ENV === "development" ? "4669" : "",
    },
    condition: process.env.NODE_ENV === "development" ? "Excellent" : "",
    size: process.env.NODE_ENV === "development" ? "3-Seater" : "",
    material: process.env.NODE_ENV === "development" ? "Genuine Leather" : "",
    description:
      process.env.NODE_ENV === "development"
        ? "Premium quality 3-seater leather sofa with excellent craftsmanship. Perfect for modern living rooms with comfortable seating and durable construction. Features high-grade leather upholstery and sturdy wooden frame."
        : "",
    stockItems: process.env.NODE_ENV === "development" ? "5" : "1",
    tags:
      process.env.NODE_ENV === "development"
        ? ["Premium", "Featured", "Comfortable"]
        : ([] as string[]),
    deliveryFee: process.env.NODE_ENV === "development" ? "500" : "0",
    deliveryTime: process.env.NODE_ENV === "development" ? "3-5 days" : "",
    heroImage: null as File | null,
    images: [] as File[],
  });
  const { toast } = useToast();
  const ai = new GoogleGenAI({
    apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      // If selling price is being updated, calculate rental prices
      if (name === "sellingPrice" && value) {
        const sellingPrice = Number.parseFloat(value);
        if (!isNaN(sellingPrice)) {
          // Calculate rental prices based on selling price
          // 6 months: 90% of selling price over 6 months
          // 9 months: 120% of selling price over 9 months
          // 12 months: 160% of selling price over 12 months
          const sixMonthsPrice = Math.round((sellingPrice * 0.9) / 6);
          const nineMonthsPrice = Math.round((sellingPrice * 1.2) / 9);
          const twelveMonthsPrice = Math.round((sellingPrice * 1.6) / 12);

          // Round to nearest 9
          const roundToNearest9 = (price: number) => {
            const remainder = price % 10;
            if (remainder <= 4) {
              return price - remainder + 9;
            } else {
              return price + (9 - remainder);
            }
          };

          return {
            ...prev,
            [name]: value,
            websitePrices: {
              sixMonths: roundToNearest9(sixMonthsPrice).toString(),
              nineMonths: roundToNearest9(nineMonthsPrice).toString(),
              twelveMonths: roundToNearest9(twelveMonthsPrice).toString(),
            },
          };
        }
      }

      return { ...prev, [name]: value };
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleHeroImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData((prev) => ({ ...prev, heroImage: e.target.files![0] }));
    }
  };

  const handleAdditionalImagesChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);

      // Check for exactly 3 images
      if (formData.images.length + files.length !== 3) {
        toast({
          title: "Invalid number of images",
          description: "Please select exactly 3 additional images.",
          variant: "destructive",
        });
        return;
      }

      // Check if files are images
      const invalidFiles = files.filter(file => !file.type.startsWith("image/"));
      if (invalidFiles.length > 0) {
        toast({
          title: "Invalid file type",
          description: "Please select only image files.",
          variant: "destructive",
        });
        return;
      }

      setFormData((prev) => ({ ...prev, images: [...prev.images, ...files] }));

      toast({
        title: "Images added successfully",
        description: "3 additional images have been uploaded.",
      });

      // Reset the input value to allow selecting the same files again if needed
      e.target.value = "";
    }
  };

  const handleImagesDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);

    // Filter only image files
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));

    if (imageFiles.length === 0) {
      toast({
        title: "Invalid files",
        description: "Please drop only image files.",
        variant: "destructive",
      });
      return;
    }

    // Check for exactly 3 images
    if (formData.images.length + imageFiles.length !== 3) {
      toast({
        title: "Invalid number of images",
        description: "Please drop exactly 3 additional images.",
        variant: "destructive",
      });
      return;
    }

    setFormData((prev) => ({
      ...prev,
      images: [...prev.images, ...imageFiles],
    }));

    toast({
      title: "Images added successfully",
      description: "3 additional images have been uploaded.",
    });
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleRemoveImage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
    
    toast({
      title: "Image removed",
      description: "Image has been removed from the gallery.",
    });
  };

  const handleGenerateDescription = async () => {
    if (!formData.name || !formData.category || !formData.material) {
      toast({
        title: "Missing information",
        description:
          "Please fill in the name, category, and material fields first.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingDescription(true);
    try {
      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-001",
        contents: `Write a concise and engaging product description for a ${formData.condition} ${formData.material} ${formData.name} in the ${formData.category} category. Keep it simple and limited to 4-5 lines. Avoid using markdown.`,
      });
      setFormData((prev) => ({ ...prev, description: response.text || "" }));
      toast({
        title: "Description generated successfully",
        description: "AI-generated product description has been added.",
      });
    } catch (error) {
      console.error("Error generating description:", error);
      toast({
        title: "Failed to generate description",
        description: "Please try again or write the description manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Enforce required fields
    if (
      !formData.name ||
      !formData.category ||
      !formData.condition ||
      !formData.buyingPrice ||
      !formData.sellingPrice ||
      !formData.websitePrices.sixMonths ||
      !formData.websitePrices.nineMonths ||
      !formData.websitePrices.twelveMonths
    ) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields before submitting.",
        variant: "destructive",
      });
      return;
    }
    
    if (!formData.heroImage || formData.images.length !== 3) {
      toast({
        title: "Images required",
        description:
          "Please upload 1 hero image and exactly 3 additional images.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.description?.trim()) {
      toast({
        title: "Description required",
        description: "Please add a product description before submitting.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    const apiFormData = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (key === "heroImage" && value instanceof File) {
        apiFormData.append("heroImage", value);
      } else if (key === "images" && Array.isArray(value)) {
        value.forEach((img) => apiFormData.append("images", img));
      } else if (key === "tags" && Array.isArray(value)) {
        apiFormData.append("tags", JSON.stringify(value));
      } else if (
        key === "websitePrices" &&
        typeof value === "object" &&
        value !== null
      ) {
        // Handle nested websitePrices object
        const websitePrices = value as {
          sixMonths: string;
          nineMonths: string;
          twelveMonths: string;
        };
        apiFormData.append("websitePrices.sixMonths", websitePrices.sixMonths);
        apiFormData.append(
          "websitePrices.nineMonths",
          websitePrices.nineMonths
        );
        apiFormData.append(
          "websitePrices.twelveMonths",
          websitePrices.twelveMonths
        );
      } else if (value !== null && value !== undefined) {
        apiFormData.append(key, value.toString());
      }
    });

    try {
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/products/add`,
        {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
          body: apiFormData,
        }
      );
      
      const data = await response.json();
      console.log("Response from server:", data);
      
      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to add product");
      }
      
      toast({
        title: "Product added successfully",
        description: `${formData.name} has been added to your inventory.`,
      });
      
      // Call the refresh callback if provided
      if (onProductAdded) {
        onProductAdded();
      }
      
      setFormData({
        name:
          process.env.NODE_ENV === "development" ? "Modern Leather Sofa" : "",
        category: process.env.NODE_ENV === "development" ? "Living Room" : "",
        subcategory: process.env.NODE_ENV === "development" ? "Sofa" : "",
        buyingPrice: process.env.NODE_ENV === "development" ? "25000" : "",
        sellingPrice: process.env.NODE_ENV === "development" ? "35000" : "",
        websitePrices: {
          sixMonths: process.env.NODE_ENV === "development" ? "5249" : "",
          nineMonths: process.env.NODE_ENV === "development" ? "4669" : "",
          twelveMonths: process.env.NODE_ENV === "development" ? "4669" : "",
        },
        condition: process.env.NODE_ENV === "development" ? "Excellent" : "",
        size: process.env.NODE_ENV === "development" ? "3-Seater" : "",
        material:
          process.env.NODE_ENV === "development" ? "Genuine Leather" : "",
        description:
          process.env.NODE_ENV === "development"
            ? "Premium quality 3-seater leather sofa with excellent craftsmanship. Perfect for modern living rooms with comfortable seating and durable construction. Features high-grade leather upholstery and sturdy wooden frame."
            : "",
        stockItems: process.env.NODE_ENV === "development" ? "5" : "1",
        tags:
          process.env.NODE_ENV === "development"
            ? ["Premium", "Featured", "Comfortable"]
            : ([] as string[]),
        deliveryFee: process.env.NODE_ENV === "development" ? "500" : "0",
        deliveryTime: process.env.NODE_ENV === "development" ? "3-5 days" : "",
        heroImage: null,
        images: [],
      });
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error adding product:", error);
      toast({
        title: "Failed to add product",
        description: error.message || "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = (e?: React.MouseEvent) => {
    e?.preventDefault();
    
    const currentStep = step;
    if (!validateStep(currentStep)) {
      let missingFields = "";
      switch (currentStep) {
        case 1:
          const step1Fields = [];
          if (!formData.name) step1Fields.push("Product Name");
          if (!formData.category) step1Fields.push("Category");
          if (!formData.buyingPrice) step1Fields.push("Buying Price");
          if (!formData.sellingPrice) step1Fields.push("Selling Price");
          if (!formData.condition) step1Fields.push("Condition");
          missingFields = step1Fields.join(", ");
          break;
        case 2:
          const step2Fields = [];
          if (!formData.description) step2Fields.push("Description");
          if (!formData.websitePrices.sixMonths) step2Fields.push("6 Months Rental Price");
          if (!formData.websitePrices.nineMonths) step2Fields.push("9 Months Rental Price");
          if (!formData.websitePrices.twelveMonths) step2Fields.push("12 Months Rental Price");
          missingFields = step2Fields.join(", ");
          break;
        case 3:
          const step3Fields = [];
          if (!formData.heroImage) step3Fields.push("Hero Image");
          if (formData.images.length !== 3) step3Fields.push("Exactly 3 Additional Images");
          missingFields = step3Fields.join(", ");
          break;
      }
      
      toast({
        title: "Cannot proceed to next step",
        description: `Please complete the following fields: ${missingFields}`,
        variant: "destructive",
      });
      return;
    }
    
    setStep((prev) => Math.min(prev + 1, 3));
  };

  const prevStep = (e?: React.MouseEvent) => {
    e?.preventDefault();
    setStep((prev) => Math.max(prev - 1, 1));
  };

  const validateStep = (currentStep: number): boolean => {
    switch (currentStep) {
      case 1:
        return (
          !!formData.name &&
          !!formData.category &&
          !!formData.buyingPrice &&
          !!formData.sellingPrice &&
          !!formData.condition
        );
      case 2:
        return (
          !!formData.description &&
          !!formData.websitePrices.sixMonths &&
          !!formData.websitePrices.nineMonths &&
          !!formData.websitePrices.twelveMonths
        );
      case 3:
        return !!formData.heroImage && formData.images.length === 3;
      default:
        return false;
    }
  };

  const handleUploadClick = () => {
    additionalImagesInputRef.current?.click();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Inventory Item</DialogTitle>
          <div className="mt-6 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step >= 1
                      ? "bg-indigo-600 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {step > 1 ? <Check className="h-5 w-5" /> : "1"}
                </div>
                <span className="text-xs mt-1">Basic Info</span>
              </div>

              <div
                className={`flex-1 h-1 mx-2 ${
                  step >= 2 ? "bg-indigo-600" : "bg-gray-200"
                }`}
              ></div>

              <div className="flex flex-col items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step >= 2
                      ? "bg-indigo-600 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {step > 2 ? <Check className="h-5 w-5" /> : "2"}
                </div>
                <span className="text-xs mt-1">Description</span>
              </div>

              <div
                className={`flex-1 h-1 mx-2 ${
                  step >= 3 ? "bg-indigo-600" : "bg-gray-200"
                }`}
              ></div>

              <div className="flex flex-col items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step >= 3
                      ? "bg-indigo-600 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  3
                </div>
                <span className="text-xs mt-1">Images</span>
              </div>
            </div>
          </div>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          {step === 1 && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Furniture Name{" "}
                    <span className="text-sm text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) =>
                      handleSelectChange("category", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Living Room">Living Room</SelectItem>
                      <SelectItem value="Dining Room">Dining Room</SelectItem>
                      <SelectItem value="Bedroom">Bedroom</SelectItem>
                      <SelectItem value="Office">Office</SelectItem>
                      <SelectItem value="Kitchen">Kitchen</SelectItem>
                      <SelectItem value="Bathroom">Bathroom</SelectItem>
                      <SelectItem value="Outdoor">Outdoor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    name="subcategory"
                    value={formData.subcategory}
                    onChange={handleChange}
                    placeholder="e.g., Sofa, Chair, Table"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="buyingPrice">Buying Price (₹) *</Label>
                  <Input
                    id="buyingPrice"
                    name="buyingPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.buyingPrice}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sellingPrice">Selling Price (₹) *</Label>
                  <Input
                    id="sellingPrice"
                    name="sellingPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.sellingPrice}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="stockItems">Stock Quantity *</Label>
                  <Input
                    id="stockItems"
                    name="stockItems"
                    type="number"
                    min="0"
                    value={formData.stockItems}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="condition">Condition *</Label>
                  <Select
                    value={formData.condition}
                    onValueChange={(value) =>
                      handleSelectChange("condition", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select condition" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="New">New</SelectItem>
                      <SelectItem value="Excellent">Excellent</SelectItem>
                      <SelectItem value="Good">Good</SelectItem>
                      <SelectItem value="Fair">Fair</SelectItem>
                      <SelectItem value="LikeNew">Like New</SelectItem>
                      <SelectItem value="Refurbished">Refurbished</SelectItem>
                      <SelectItem value="Used">Used</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="size">Size</Label>
                  <Input
                    id="size"
                    name="size"
                    value={formData.size}
                    onChange={handleChange}
                    placeholder="e.g., Small, Medium, Large"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="material">Material</Label>
                  <Input
                    id="material"
                    name="material"
                    value={formData.material}
                    onChange={handleChange}
                    placeholder="e.g., Wood, Metal"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deliveryFee">Delivery Fee (₹)</Label>
                  <Input
                    id="deliveryFee"
                    name="deliveryFee"
                    type="number"
                    min="0"
                    value={formData.deliveryFee}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryTime">Delivery Time</Label>
                  <Input
                    id="deliveryTime"
                    name="deliveryTime"
                    value={formData.deliveryTime}
                    onChange={handleChange}
                    placeholder="e.g., 2-3 days"
                  />
                </div>

                <div className="col-span-2">
                  <h3 className="text-lg font-medium mb-2">Rental Pricing</h3>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sixMonths">6 Months Rental (₹/mo) *</Label>
                  <Input
                    id="sixMonths"
                    name="websitePrices.sixMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.sixMonths}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        websitePrices: {
                          ...prev.websitePrices,
                          sixMonths: e.target.value,
                        },
                      }))
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nineMonths">9 Months Rental (₹/mo) *</Label>
                  <Input
                    id="nineMonths"
                    name="websitePrices.nineMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.nineMonths}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        websitePrices: {
                          ...prev.websitePrices,
                          nineMonths: e.target.value,
                        },
                      }))
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="twelveMonths">
                    12 Months Rental (₹/mo) *
                  </Label>
                  <Input
                    id="twelveMonths"
                    name="websitePrices.twelveMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.twelveMonths}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        websitePrices: {
                          ...prev.websitePrices,
                          twelveMonths: e.target.value,
                        },
                      }))
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    name="tags"
                    value={formData.tags.join(", ")}
                    onChange={(e) => {
                      const tagsArray = e.target.value
                        .split(",")
                        .map((tag) => tag.trim())
                        .filter((tag) => tag !== "");
                      setFormData((prev) => ({ ...prev, tags: tagsArray }));
                    }}
                    placeholder="e.g., Premium, Featured"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="description">Description</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleGenerateDescription}
                    disabled={isGeneratingDescription}
                  >
                    {isGeneratingDescription ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3" />
                    )}
                    {isGeneratingDescription
                      ? "Generating..."
                      : "Generate with AI"}
                  </Button>
                </div>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={5}
                />
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="heroImage">Hero Image * (Main Image)</Label>
                <Input
                  id="heroImage"
                  type="file"
                  accept="image/*"
                  onChange={handleHeroImageChange}
                  required
                />
                {formData.heroImage && (
                  <img
                    src={URL.createObjectURL(formData.heroImage)}
                    alt="Hero Preview"
                    className="w-32 h-32 object-cover rounded-md mt-2"
                  />
                )}
              </div>
              <div className="space-y-2">
                <Label>Additional Images * (Exactly 3 required)</Label>

                {/* Hidden file input */}
                <input
                  ref={additionalImagesInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleAdditionalImagesChange}
                  className="hidden"
                />

                {/* Drop zone with click functionality */}
                <div
                  className="border-2 border-dashed border-gray-300 hover:border-gray-400 p-6 rounded-md cursor-pointer transition-colors"
                  onDrop={handleImagesDrop}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onClick={handleUploadClick}
                >
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Upload className="h-8 w-8 text-gray-400" />
                    <p className="text-center text-gray-600">
                      <span className="font-medium">Click to upload</span> or
                      drag and drop images here
                    </p>
                    <p className="text-xs text-gray-500">
                      PNG, JPG, GIF up to 10MB each (exactly 3 required)
                    </p>
                  </div>
                </div>

                {formData.images.length > 0 && (
                  <div className="grid grid-cols-3 gap-4 mt-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Preview ${index}`}
                          className="w-full h-32 object-cover rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2 h-6 w-6 p-0"
                          onClick={(e) => {
                            e.preventDefault();
                            handleRemoveImage(index);
                          }}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter className="mt-6">
            {step > 1 && (
              <Button type="button" variant="outline" onClick={prevStep}>
                Previous
              </Button>
            )}
            {step < 3 && (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!validateStep(step)}
              >
                Next
              </Button>
            )}
            {step === 3 && (
              <Button type="submit" disabled={isLoading || !validateStep(step)}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  "Add Item"
                )}
              </Button>
            )}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
