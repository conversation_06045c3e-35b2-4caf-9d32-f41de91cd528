"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Package, CheckCircle, ShoppingBag, Wrench } from "lucide-react"

export function InventorySkeleton() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-40" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-32" />
        </div>
      </div>

      {/* Stats Cards Loading State */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { icon: Package, color: "primary" },
          { icon: CheckCircle, color: "green" },
          { icon: ShoppingBag, color: "primary" },
          { icon: Wrench, color: "yellow" },
        ].map((item, index) => (
          <Card key={index}>
            <CardContent className="p-6 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`bg-${item.color === "primary" ? "primary/10" : `${item.color}-100`} p-3 rounded-lg`}>
                  <item.icon
                    className={`h-6 w-6 ${item.color === "primary" ? "text-primary" : `text-${item.color}-600`}`}
                  />
                </div>
                <div>
                  <Skeleton className="h-8 w-16 mb-1" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <Skeleton className="h-4 w-12" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters Loading State */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <Skeleton className="h-10 w-full md:w-1/2" />
        <div className="flex flex-wrap gap-2 items-center w-full md:w-auto">
          <Skeleton className="h-10 w-[180px]" />
          <Skeleton className="h-10 w-[180px]" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>

      {/* Table Loading State */}
      <div className="bg-white rounded-lg border">
        <div className="p-4 border-b">
          <div className="grid grid-cols-8 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-full" />
            ))}
          </div>
        </div>

        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="p-4 border-b">
            <div className="grid grid-cols-8 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <Skeleton key={i} className="h-6 w-full" />
              ))}
            </div>
          </div>
        ))}

        <div className="p-4 flex justify-between items-center">
          <Skeleton className="h-5 w-40" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
      </div>
    </div>
  )
}
