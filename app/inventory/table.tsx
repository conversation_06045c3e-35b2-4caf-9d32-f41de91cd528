"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, CheckCircle, Eye, EyeOff, Edit, Trash, Loader2, ExternalLink } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { auth } from "@/lib/firebase"
import { Badge } from "@/components/ui/badge"
import { ProductDetailsModal } from "./product-details-modal"
import { EditProductDialog } from "./edit-product"
import type { ProductType } from "@/types/product"

interface InventoryTableProps {
  items: ProductType[]
}

export function InventoryTable({ items }: InventoryTableProps) {
  const [products, setProducts] = useState(items)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [loadingProductId, setLoadingProductId] = useState<string | null>(null)
  const { toast } = useToast()

  const getStatusBadge = (stockItems: number) => {
    if (stockItems > 10) {
      return (
        <Badge variant="success" className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="w-3 h-3 mr-1" /> Available
        </Badge>
      )
    } else if (stockItems > 0) {
      return (
        <Badge variant="warning" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          Low Stock
        </Badge>
      )
    } else {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 hover:bg-red-100">
          Out of Stock
        </Badge>
      )
    }
  }

  const toggleVisibility = async (id: string, currentVisibility: boolean) => {
    try {
      setLoadingProductId(id)
      const token = await auth.currentUser?.getIdToken()
      if (!token) {
        toast({ title: "Unauthorized", variant: "destructive" })
        return
      }

      const formData = new FormData()
      formData.append("id", id)
      formData.append("showOnWebsite", (!currentVisibility).toString())

      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/products/update`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to toggle visibility")
      }

      const updatedProduct = await response.json()

      // Update the product visibility in the state
      setProducts((prev) =>
        prev.map((item) => (item.id === id ? { ...item, showOnWebsite: updatedProduct.showOnWebsite } : item)),
      )

      toast({
        title: `Product ${updatedProduct.showOnWebsite ? "shown on" : "hidden from"} website`,
        variant: "default",
      })
    } catch (error) {
      console.error("Error toggling visibility:", error)
      toast({
        title: "Failed to update visibility",
        variant: "destructive",
      })
    } finally {
      setLoadingProductId(null)
    }
  }

  const handleViewDetails = (product: any) => {
    setSelectedProduct(product)
    setIsDetailsModalOpen(true)
  }

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setIsEditModalOpen(true)
  }

  const handleViewOnWebsite = (productId: string) => {
    window.open(`https://l2lrentals.in/products/${productId}`, '_blank')
  }

  return (
    <>
      <div className="bg-white rounded-lg border">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[30px] sticky left-0 bg-white z-10">
                  <Checkbox />
                </TableHead>
                <TableHead className="min-w-[80px] sticky left-[30px] bg-white z-10">Image</TableHead>
                <TableHead className="min-w-[150px]">Item Name</TableHead>
                <TableHead className="min-w-[80px] hidden sm:table-cell">Status</TableHead>
                <TableHead className="min-w-[100px]">Stock</TableHead>
                <TableHead className="min-w-[100px] hidden md:table-cell">Rented</TableHead>
                <TableHead className="min-w-[100px] hidden lg:table-cell">Visibility</TableHead>
                <TableHead className="min-w-[120px]">Website</TableHead>
                <TableHead className="min-w-[80px]">Actions</TableHead>
              </TableRow>
            </TableHeader>  return (
    <>
      <div className="bg-white rounded-lg border">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[30px] sticky left-0 bg-white z-10">
                  <Checkbox />
                </TableHead>
                <TableHead className="min-w-[80px] sticky left-[30px] bg-white z-10">Image</TableHead>
                <TableHead className="min-w-[150px]">Item Name</TableHead>
                <TableHead className="min-w-[80px] hidden sm:table-cell">Status</TableHead>
                <TableHead className="min-w-[100px]">Stock</TableHead>
                <TableHead className="min-w-[100px] hidden md:table-cell">Rented</TableHead>
                <TableHead className="min-w-[100px] hidden lg:table-cell">Visibility</TableHead>
                <TableHead className="min-w-[120px]">Website</TableHead>
                <TableHead className="min-w-[80px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="sticky left-0 bg-white z-10">
                    <Checkbox />
                  </TableCell>
                  <TableCell className="sticky left-[30px] bg-white z-10">
                    <img
                      src={item.heroImage || "/placeholder.svg?height=80&width=80"}
                      alt={item.name}
                      className="w-10 h-10 object-cover rounded-md"
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="max-w-[200px] truncate" title={item.name}>
                      {item.name}
                    </div>
                    <div className="sm:hidden text-xs text-gray-500 mt-1">
                      {getStatusBadge(item.stockItems)}
                    </div>
                  </TableCell>
                  <TableCell className="hidden sm:table-cell">{getStatusBadge(item.stockItems)}</TableCell>
                  <TableCell>
                    <div className="text-sm font-medium">{item.stockItems}</div>
                    <div className="md:hidden text-xs text-gray-500">
                      Rented: {item.rentedItems || 0}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">{item.rentedItems || 0}</TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Badge variant={item.showOnWebsite ? "outline" : "secondary"}>
                      {item.showOnWebsite ? "Visible" : "Hidden"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewOnWebsite(item.id)}
                      className="flex items-center gap-1 text-xs px-2 py-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                      <span className="hidden sm:inline">View</span>
                    </Button>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(item)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditProduct(item)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Item
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleVisibility(item.id, item.showOnWebsite)}>
                          {item.showOnWebsite ? (
                            <>
                              <EyeOff className="mr-2 h-4 w-4" />
                              Hide from Website
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-4 w-4" />
                              Show on Website
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash className="mr-2 h-4 w-4" />
                          Delete Item
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between p-4 border-t">
          <div className="text-sm text-muted-foreground">Showing {products.length} items</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </div>
      </div>

      <ProductDetailsModal open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen} product={selectedProduct} />

      <EditProductDialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen} product={selectedProduct} />
    </>
  )
}
