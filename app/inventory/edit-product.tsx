"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Sparkles, Save } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { auth } from "@/lib/firebase"

interface EditProductProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: any | null
}

export function EditProductDialog({ open, onOpenChange, product }: EditProductProps) {
  const [step, setStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false)
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    category: "",
    subcategory: "",
    buyingPrice: "",
    sellingPrice: "",
    websitePrices: {
      sixMonths: "",
      nineMonths: "",
      twelveMonths: "",
    },
    condition: "",
    size: "",
    material: "",
    description: "",
    stockItems: "1",
    tags: [] as string[],
    deliveryFee: "0",
    deliveryTime: "",
    rating: "0",
    reviewCount: "0",
    showOnWebsite: true,
    heroImage: null as File | null,
    existingHeroImage: "",
    images: [] as File[],
    existingImages: [] as string[],
  })
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (product) {
      // Extract website prices from the product
      const websitePrices = product.websitePrices || {}

      setFormData({
        id: product.id || "",
        name: product.name || "",
        category: product.category || "",
        subcategory: product.subcategory || "",
        buyingPrice: product.buyingPrice?.toString() || "",
        sellingPrice: product.sellingPrice?.toString() || "",
        websitePrices: {
          sixMonths: websitePrices.sixMonths?.toString() || "",
          nineMonths: websitePrices.nineMonths?.toString() || "",
          twelveMonths: websitePrices.twelveMonths?.toString() || "",
        },
        condition: product.condition || "",
        size: product.size || "",
        material: product.material || "",
        description: product.description || "",
        stockItems: product.stockItems?.toString() || "1",
        tags: product.tags || [],
        deliveryFee: product.deliveryFee?.toString() || "0",
        deliveryTime: product.deliveryTime || "",
        rating: product.rating?.toString() || "0",
        reviewCount: product.reviewCount?.toString() || "0",
        showOnWebsite: product.showOnWebsite || false,
        heroImage: null,
        existingHeroImage: product.heroImage || "",
        images: [],
        existingImages: product.images || [],
      })
    }
  }, [product])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    // Check if this is a nested property (e.g., websitePrices.sixMonths)
    if (name.includes(".")) {
      const [parent, child] = name.split(".")
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as Record<string, unknown>),
          [child]: value,
        },
      }))
    } else if (name === "sellingPrice" && value) {
      // If selling price is being updated, calculate rental prices
      const sellingPrice = Number.parseFloat(value)
      if (!isNaN(sellingPrice)) {
        // Calculate rental prices based on selling price
        // 6 months: 90% of selling price over 6 months
        // 9 months: 120% of selling price over 9 months
        // 12 months: 160% of selling price over 12 months
        const sixMonthsPrice = Math.round((sellingPrice * 0.9) / 6)
        const nineMonthsPrice = Math.round((sellingPrice * 1.2) / 9)
        const twelveMonthsPrice = Math.round((sellingPrice * 1.6) / 12)

        // Round to nearest 9
        const roundToNearest9 = (price: number) => {
          const remainder = price % 10
          if (remainder <= 4) {
            return price - remainder + 9
          } else {
            return price + (9 - remainder)
          }
        }

        setFormData((prev) => ({
          ...prev,
          [name]: value,
          websitePrices: {
            ...prev.websitePrices,
            sixMonths: roundToNearest9(sixMonthsPrice).toString(),
            nineMonths: roundToNearest9(nineMonthsPrice).toString(),
            twelveMonths: roundToNearest9(twelveMonthsPrice).toString(),
          },
        }))
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }))
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }))
    }
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleHeroImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData((prev) => ({ ...prev, heroImage: e.target.files![0] }))
    }
  }

  const handleAdditionalImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files)
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, ...newFiles],
      }))
    }
  }

  const handleRemoveExistingImage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      existingImages: prev.existingImages.filter((_, i) => i !== index),
    }))
  }

  const handleRemoveNewImage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }))
  }

  const handleGenerateDescription = async () => {
    if (!formData.name || !formData.category) {
      toast({
        title: "Missing information",
        description: "Please fill in the name and category fields first.",
        variant: "destructive",
      })
      return
    }

    setIsGeneratingDescription(true)
    try {
      // Mock AI generation for now
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const generatedDescription = `This ${formData.condition.toLowerCase()} ${formData.name.toLowerCase()} is perfect for your ${formData.category.toLowerCase()} space. Crafted with ${formData.material || "quality materials"}, it offers both style and functionality. The ${formData.size || "standard"} size makes it versatile for various settings. Rent today and elevate your space with this beautiful piece.`

      setFormData((prev) => ({ ...prev, description: generatedDescription }))
      toast({
        title: "Description generated",
        description: "AI-generated description has been added to the form.",
      })
    } catch (error) {
      console.error("Error generating description:", error)
      toast({
        title: "Failed to generate description",
        description: "Please try again or write your own description.",
        variant: "destructive",
      })
    } finally {
      setIsGeneratingDescription(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Create FormData object for API submission
      const token = await auth.currentUser?.getIdToken()
      if (!token) {
        toast({ title: "Unauthorized", variant: "destructive" })
        return
      }

      const apiFormData = new FormData()

      // Add all text fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === "websitePrices" && typeof value === "object" && value !== null) {
          // Handle nested websitePrices object
          const websitePrices = value as { sixMonths: string; nineMonths: string; twelveMonths: string }
          apiFormData.append("websitePrices.sixMonths", websitePrices.sixMonths)
          apiFormData.append("websitePrices.nineMonths", websitePrices.nineMonths)
          apiFormData.append("websitePrices.twelveMonths", websitePrices.twelveMonths)
        } else if (key === "tags" && Array.isArray(value)) {
          // Handle tags array
          apiFormData.append("tags", JSON.stringify(value))
        } else if (key !== "heroImage" && key !== "images" && key !== "existingHeroImage" && key !== "existingImages") {
          apiFormData.append(key, value !== null && value !== undefined ? value.toString() : "")
        }
      })

      // Add hero image if changed
      if (formData.heroImage) {
        apiFormData.append("heroImage", formData.heroImage)
      }

      // Add existing images that weren't removed
      if (formData.existingImages.length > 0) {
        apiFormData.append("existingImages", JSON.stringify(formData.existingImages))
      }

      // Add new images
      formData.images.forEach((image) => {
        apiFormData.append("newImages", image)
      })

      // Make the actual API call
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/products/update`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: apiFormData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update product")
      }

      const updatedProduct = await response.json()
      console.log("Updated product:", updatedProduct)

      toast({
        title: "Product updated successfully",
        description: `${updatedProduct.name} has been updated in your inventory.`,
      })

      // Close dialog and refresh data
      onOpenChange(false)
      router.refresh()
    } catch (error) {
      console.error("Error updating product:", error)
      toast({
        title: "Failed to update product",
        description: error instanceof Error ? error.message : "Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const nextStep = () => setStep((prev) => Math.min(prev + 1, 3))
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1))

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Product - Step {step} of 3</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-medium">
                  {step}
                </div>
                <span className="font-medium">
                  {step === 1 ? "Basic Information" : step === 2 ? "Details & Description" : "Images"}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-3 h-3 rounded-full ${step >= 1 ? "bg-purple-600" : "bg-gray-200"}`}></div>
                <div className={`w-3 h-3 rounded-full ${step >= 2 ? "bg-purple-600" : "bg-gray-200"}`}></div>
                <div className={`w-3 h-3 rounded-full ${step >= 3 ? "bg-purple-600" : "bg-gray-200"}`}></div>
              </div>
            </div>
          </div>

          {step === 1 && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Item Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter item name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">
                    Category <span className="text-red-500">*</span>
                  </Label>
                  <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Living Room">Living Room</SelectItem>
                      <SelectItem value="Dining Room">Dining Room</SelectItem>
                      <SelectItem value="Bedroom">Bedroom</SelectItem>
                      <SelectItem value="Office">Office</SelectItem>
                      <SelectItem value="Kitchen">Kitchen</SelectItem>
                      <SelectItem value="Bathroom">Bathroom</SelectItem>
                      <SelectItem value="Outdoor">Outdoor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    name="subcategory"
                    value={formData.subcategory}
                    onChange={handleChange}
                    placeholder="e.g., Sofa, Chair, Table"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="buyingPrice">
                    Buying Price (₹) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="buyingPrice"
                    name="buyingPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.buyingPrice}
                    onChange={handleChange}
                    placeholder="0.00"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sellingPrice">
                    Selling Price (₹) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="sellingPrice"
                    name="sellingPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.sellingPrice}
                    onChange={handleChange}
                    placeholder="0.00"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stockItems">
                    Stock Quantity <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="stockItems"
                    name="stockItems"
                    type="number"
                    min="0"
                    value={formData.stockItems}
                    onChange={handleChange}
                    placeholder="0"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="condition">
                    Condition <span className="text-red-500">*</span>
                  </Label>
                  <Select value={formData.condition} onValueChange={(value) => handleSelectChange("condition", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select condition" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Excellent">Excellent</SelectItem>
                      <SelectItem value="Good">Good</SelectItem>
                      <SelectItem value="Fair">Fair</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rating">Rating</Label>
                  <Input
                    id="rating"
                    name="rating"
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    value={formData.rating}
                    onChange={handleChange}
                    placeholder="5.0"
                  />
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="size">Size</Label>
                  <Input
                    id="size"
                    name="size"
                    value={formData.size}
                    onChange={handleChange}
                    placeholder="e.g., Small, Medium, Large or dimensions"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="material">Material</Label>
                  <Input
                    id="material"
                    name="material"
                    value={formData.material}
                    onChange={handleChange}
                    placeholder="e.g., Wood, Metal, Fabric"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deliveryFee">Delivery Fee (₹)</Label>
                  <Input
                    id="deliveryFee"
                    name="deliveryFee"
                    type="number"
                    min="0"
                    value={formData.deliveryFee}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deliveryTime">Delivery Time</Label>
                  <Input
                    id="deliveryTime"
                    name="deliveryTime"
                    value={formData.deliveryTime}
                    onChange={handleChange}
                    placeholder="e.g., 2-3 days"
                  />
                </div>

                <div className="col-span-2">
                  <h3 className="text-lg font-medium mb-2">Rental Pricing</h3>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sixMonths">6 Months Rental (₹/mo) *</Label>
                  <Input
                    id="sixMonths"
                    name="websitePrices.sixMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.sixMonths}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nineMonths">9 Months Rental (₹/mo) *</Label>
                  <Input
                    id="nineMonths"
                    name="websitePrices.nineMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.nineMonths}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="twelveMonths">12 Months Rental (₹/mo) *</Label>
                  <Input
                    id="twelveMonths"
                    name="websitePrices.twelveMonths"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.websitePrices.twelveMonths}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="description">Description</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleGenerateDescription}
                    disabled={isGeneratingDescription}
                    className="flex items-center gap-1"
                  >
                    {isGeneratingDescription ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-3 w-3" />
                        Generate with AI
                      </>
                    )}
                  </Button>
                </div>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Enter item description"
                  rows={5}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma-separated)</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.tags.join(", ")}
                  onChange={(e) => {
                    const tagsArray = e.target.value
                      .split(",")
                      .map((tag) => tag.trim())
                      .filter((tag) => tag !== "")
                    setFormData((prev) => ({ ...prev, tags: tagsArray }))
                  }}
                  placeholder="e.g., Premium, Featured"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showOnWebsite"
                  checked={formData.showOnWebsite}
                  onChange={(e) => handleCheckboxChange("showOnWebsite", e.target.checked)}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <Label htmlFor="showOnWebsite" className="cursor-pointer">
                  Show on website
                </Label>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="heroImage">Hero Image</Label>
                <div className="flex items-center gap-4">
                  {formData.existingHeroImage && !formData.heroImage && (
                    <div className="relative">
                      <img
                        src={formData.existingHeroImage || "/placeholder.svg"}
                        alt="Current hero image"
                        className="w-32 h-32 object-cover rounded-md"
                      />
                      <span className="text-xs text-muted-foreground block mt-1">Current image</span>
                    </div>
                  )}
                  <div className="flex-1">
                    <Input id="heroImage" type="file" accept="image/*" onChange={handleHeroImageChange} />
                    {formData.heroImage && (
                      <div className="mt-2">
                        <img
                          src={URL.createObjectURL(formData.heroImage) || "/placeholder.svg"}
                          alt="New hero image"
                          className="w-32 h-32 object-cover rounded-md"
                        />
                        <span className="text-xs text-muted-foreground block mt-1">New image</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Additional Images</Label>

                {formData.existingImages.length > 0 && (
                  <>
                    <h4 className="text-sm font-medium text-muted-foreground mt-4">Current Images</h4>
                    <div className="grid grid-cols-3 gap-4 mt-2">
                      {formData.existingImages.map((image, index) => (
                        <div key={`existing-${index}`} className="relative">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`Existing ${index}`}
                            className="w-full h-32 object-cover rounded-md"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2 h-6 w-6 p-0"
                            onClick={() => handleRemoveExistingImage(index)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  </>
                )}

                <h4 className="text-sm font-medium text-muted-foreground mt-4">Add New Images</h4>
                <Input id="newImages" type="file" accept="image/*" multiple onChange={handleAdditionalImagesChange} />

                {formData.images.length > 0 && (
                  <div className="grid grid-cols-3 gap-4 mt-2">
                    {formData.images.map((image, index) => (
                      <div key={`new-${index}`} className="relative">
                        <img
                          src={URL.createObjectURL(image) || "/placeholder.svg"}
                          alt={`New ${index}`}
                          className="w-full h-32 object-cover rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2 h-6 w-6 p-0"
                          onClick={() => handleRemoveNewImage(index)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter className="mt-6 flex items-center justify-between">
            <div>
              {step > 1 && (
                <Button type="button" variant="outline" onClick={prevStep}>
                  Previous
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              {step < 3 ? (
                <Button type="button" onClick={nextStep}>
                  Next
                </Button>
              ) : (
                <Button type="submit" disabled={isLoading} className="bg-purple-600 hover:bg-purple-700">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              )}
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
