"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, Dialog<PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import type { ProductType } from "@/types/product"
import {
  CalendarDays,
  DollarSign,
  Package,
  Star,
  Ruler,
  Layers,
  Tag,
  Truck,
  Clock,
  ShoppingBag,
  CreditCard,
} from "lucide-react"

interface ProductDetailsModalProps {
  product: ProductType | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProductDetailsModal({ product, open, onOpenChange }: ProductDetailsModalProps) {
  const [activeTab, setActiveTab] = useState("details")

  if (!product) return null

  const getStatusBadge = (stockItems: number) => {
    if (stockItems === 0) return <Badge variant="destructive">Out of Stock</Badge>
    if (stockItems < 10) return <Badge variant="warning">Low Stock</Badge>
    return <Badge variant="success">Available</Badge>
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">{product.name}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
            <TabsTrigger value="description">Description</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <img
                  src={product.heroImage || "/placeholder.svg?height=300&width=300"}
                  alt={product.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-2 flex-wrap">
                  {getStatusBadge(product.stockItems)}
                  <Badge variant="outline">{product.condition}</Badge>
                  <Badge variant="secondary">{product.category}</Badge>
                  {product.subcategory && <Badge variant="secondary">{product.subcategory}</Badge>}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Stock Items</p>
                      <p className="text-lg font-bold">{product.stockItems}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Rented Items</p>
                      <p className="text-lg font-bold">{product.rentedItems || 0}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Rating</p>
                      <p className="text-lg font-bold">
                        {product.rating}/5 ({product.reviewCount} reviews)
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Added On</p>
                      <p className="text-lg font-bold">{new Date(product.createdAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Ruler className="h-5 w-5 text-muted-foreground" />
                    <p className="text-sm font-medium">
                      Size: <span className="font-normal">{product.size || "Not specified"}</span>
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Layers className="h-5 w-5 text-muted-foreground" />
                    <p className="text-sm font-medium">
                      Material: <span className="font-normal">{product.material || "Not specified"}</span>
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Tag className="h-5 w-5 text-muted-foreground" />
                    <p className="text-sm font-medium">
                      Tags:
                      <span className="font-normal">
                        {product.tags && product.tags.length > 0 ? product.tags.join(", ") : "None"}
                      </span>
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Truck className="h-5 w-5 text-muted-foreground" />
                    <p className="text-sm font-medium">
                      Delivery Fee: <span className="font-normal">₹{product.deliveryFee?.toFixed(2) || "0.00"}</span>
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-muted-foreground" />
                    <p className="text-sm font-medium">
                      Delivery Time: <span className="font-normal">{product.deliveryTime || "Not specified"}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-semibold">Purchase Pricing</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Buying Price</p>
                      <p className="text-lg font-bold">₹{product.buyingPrice.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Selling Price</p>
                      <p className="text-lg font-bold">₹{product.sellingPrice.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-semibold">Rental Pricing</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-5 w-5 text-indigo-600" />
                      <h4 className="font-medium">6 Months Plan</h4>
                    </div>
                    <p className="text-2xl font-bold">
                      ₹{product.websitePrices?.sixMonths?.toFixed(2) || "0.00"}
                      <span className="text-sm font-normal text-gray-500">/mo</span>
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Total: ₹{((product.websitePrices?.sixMonths || 0) * 6).toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      ({Math.round((((product.websitePrices?.sixMonths || 0) * 6) / product.sellingPrice) * 100)}% of
                      selling price)
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg bg-indigo-50">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-5 w-5 text-indigo-600" />
                      <h4 className="font-medium">9 Months Plan</h4>
                    </div>
                    <p className="text-2xl font-bold">
                      ₹{product.websitePrices?.nineMonths?.toFixed(2) || "0.00"}
                      <span className="text-sm font-normal text-gray-500">/mo</span>
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Total: ₹{((product.websitePrices?.nineMonths || 0) * 9).toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      ({Math.round((((product.websitePrices?.nineMonths || 0) * 9) / product.sellingPrice) * 100)}% of
                      selling price)
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-5 w-5 text-indigo-600" />
                      <h4 className="font-medium">12 Months Plan</h4>
                    </div>
                    <p className="text-2xl font-bold">
                      ₹{product.websitePrices?.twelveMonths?.toFixed(2) || "0.00"}
                      <span className="text-sm font-normal text-gray-500">/mo</span>
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Total: ₹{((product.websitePrices?.twelveMonths || 0) * 12).toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      ({Math.round((((product.websitePrices?.twelveMonths || 0) * 12) / product.sellingPrice) * 100)}%
                      of selling price)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="images">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="col-span-full">
                <p className="font-medium mb-2">Hero Image</p>
                <img
                  src={product.heroImage || "/placeholder.svg?height=300&width=300"}
                  alt="Hero"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>

              <p className="col-span-full font-medium mt-4 mb-2">Additional Images</p>
              {product.images.map((image, index) => (
                <img
                  key={index}
                  src={image || "/placeholder.svg?height=200&width=200"}
                  alt={`Product ${index + 1}`}
                  className="w-full h-40 object-cover rounded-lg"
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="description">
            <div className="prose max-w-none">
              <h3>Product Description</h3>
              <div className="whitespace-pre-wrap">{product.description || "No description available."}</div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button>Edit Product</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
