"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Package, CheckCircle, ShoppingBag, Wrench } from "lucide-react"

interface StatsCardsProps {
  inventoryData: {
    totalItems: { count: number; change: number }
    availableItems: { count: number; change: number }
    rentedItems: { count: number; change: number }
    maintenanceItems: { count: number; change: number }
  }
}

export function StatsCards({ inventoryData }: StatsCardsProps) {
  const formatChange = (change: number) => {
    if (change > 0) return `+${change}%`
    if (change < 0) return `${change}%`
    return "0%"
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-gray-500"
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-primary/10 p-3 rounded-lg">
              <Package className="h-6 w-6 text-primary" />
            </div>
            <div>
              <div className="text-2xl font-bold">{inventoryData.totalItems.count}</div>
              <div className="text-sm text-muted-foreground">Total Items</div>
            </div>
          </div>
          <div className={`text-sm ${getChangeColor(inventoryData.totalItems.change)}`}>
            {formatChange(inventoryData.totalItems.change)}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-green-100 p-3 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">{inventoryData.availableItems.count}</div>
              <div className="text-sm text-muted-foreground">Available Items</div>
            </div>
          </div>
          <div className={`text-sm ${getChangeColor(inventoryData.availableItems.change)}`}>
            {formatChange(inventoryData.availableItems.change)}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-primary/10 p-3 rounded-lg">
              <ShoppingBag className="h-6 w-6 text-primary" />
            </div>
            <div>
              <div className="text-2xl font-bold">{inventoryData.rentedItems.count}</div>
              <div className="text-sm text-muted-foreground">Rented Items</div>
            </div>
          </div>
          <div className={`text-sm ${getChangeColor(inventoryData.rentedItems.change)}`}>
            {formatChange(inventoryData.rentedItems.change)}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="bg-yellow-100 p-3 rounded-lg">
              <Wrench className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">{inventoryData.maintenanceItems.count}</div>
              <div className="text-sm text-muted-foreground">In Maintenance</div>
            </div>
          </div>
          <div className={`text-sm ${getChangeColor(inventoryData.maintenanceItems.change)}`}>
            {formatChange(inventoryData.maintenanceItems.change)}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
