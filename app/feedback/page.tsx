"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Star, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { FeedbackSkeleton } from "./loading-skeleton"

interface Review {
  id: string
  product: string
  productId: string
  customer: string
  customerId: string
  rating: number
  review: string
  date: string
  isNew?: boolean
  hasResponse?: boolean
  response?: {
    id: string
    response: string
    createdAt: string
    updatedAt: string
  }
}

interface Product {
  name: string
  totalReviews: number
  rating: number
  priceRange: string
  inStock: boolean | string
}

export default function FeedbackPage() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState("date")
  const [feedbackData, setFeedbackData] = useState({
    averageRating: 4.2,
    totalReviews: 156,
    positiveReviews: 87,
    topProducts: [] as Product[],
    reviews: [] as Review[],
  })

  // Dialog state
  const [isResponseDialogOpen, setIsResponseDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [responseText, setResponseText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
      }
    })
    return () => unsubscribe()
  }, [router])

  // Fetch data when user is available or sort order changes
  useEffect(() => {
    if (user) {
      fetchFeedbackData()
    }
  }, [user, sortBy])

  const fetchFeedbackData = async () => {
    try {
      setIsLoading(true)

      // Get the authentication token
      const token = await user.getIdToken()

      // Fetch summary data
      const summaryResponse = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/feedback/summary`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!summaryResponse.ok) {
        throw new Error("Failed to fetch feedback summary")
      }

      const summaryData = await summaryResponse.json()

      // Fetch top products
      const topProductsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/feedback/top-products?limit=3`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!topProductsResponse.ok) {
        throw new Error("Failed to fetch top products")
      }

      const topProductsData = await topProductsResponse.json()

      // Fetch reviews
      const reviewsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/feedback/reviews?sortBy=${sortBy}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!reviewsResponse.ok) {
        throw new Error("Failed to fetch reviews")
      }

      const reviewsData = await reviewsResponse.json()

      // Update state with fetched data
      setFeedbackData({
        averageRating: summaryData.summary.averageRating,
        totalReviews: summaryData.summary.totalReviews,
        positiveReviews: summaryData.summary.positiveReviews,
        topProducts: topProductsData.topProducts.map((product: any) => ({
          name: product.name,
          totalReviews: product.totalReviews,
          rating: product.rating,
          priceRange: product.priceRange,
          inStock: product.inStock,
        })),
        reviews: reviewsData.reviews,
      })

      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching feedback data:", error)
      toast({
        title: "Failed to fetch feedback data",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const filteredReviews = feedbackData.reviews
    .filter(
      (review) =>
        review.product.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.review.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort((a, b) => {
      if (sortBy === "date") {
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      } else if (sortBy === "rating-high") {
        return b.rating - a.rating
      } else if (sortBy === "rating-low") {
        return a.rating - b.rating
      }
      return 0
    })

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "2-digit", day: "2-digit" }
    return new Date(dateString).toLocaleDateString("en-US", options)
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <Star key={i} className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`} />
        ))}
      </div>
    )
  }

  // Handler for viewing review details
  const handleViewReviewDetails = (review: Review) => {
    setSelectedReview(review)
    setIsViewDialogOpen(true)
  }

  // Handler for responding to a review
  const handleRespondToReview = async (review: Review) => {
    setSelectedReview(review)

    // If the review already has a response, fetch it
    if (review.hasResponse) {
      try {
        const token = await user.getIdToken()
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/feedback/respond?reviewId=${review.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        )

        if (!response.ok) {
          throw new Error("Failed to fetch review response")
        }

        const data = await response.json()

        if (data.success && data.hasResponse) {
          setResponseText(data.response.response)
        }
      } catch (error) {
        console.error("Error fetching review response:", error)
        toast({
          title: "Error",
          description: "Failed to fetch existing response",
          variant: "destructive",
        })
      }
    } else {
      setResponseText("")
    }

    setIsResponseDialogOpen(true)
  }

  // Handler for submitting a response to a review
  const handleSubmitResponse = async () => {
    if (!selectedReview || !responseText.trim()) {
      toast({
        title: "Error",
        description: "Please enter a response",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      const token = await user.getIdToken()
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/feedback/respond`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          reviewId: selectedReview.id,
          response: responseText,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to submit response")
      }

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: selectedReview.hasResponse ? "Response updated successfully" : "Response submitted successfully",
        })

        // Update the review in the state
        const updatedReviews = feedbackData.reviews.map((review) => {
          if (review.id === selectedReview.id) {
            return {
              ...review,
              hasResponse: true,
              response: {
                id: data.response.id,
                response: data.response.response,
                createdAt: data.response.createdAt,
                updatedAt: data.response.updatedAt,
              },
            }
          }
          return review
        })

        setFeedbackData({
          ...feedbackData,
          reviews: updatedReviews,
        })

        // Close the dialog
        setIsResponseDialogOpen(false)
        setSelectedReview(null)
        setResponseText("")
      } else {
        throw new Error(data.error || "Failed to submit response")
      }
    } catch (error) {
      console.error("Error submitting response:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit response",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handler for flagging a review
  const handleFlagReview = (review: Review) => {
    toast({
      title: "Feature not implemented",
      description: "Flagging reviews is not yet implemented",
      variant: "default",
    })
  }

  if (isLoading) {
    return <FeedbackSkeleton />
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Feedback</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center h-full">
            <h2 className="text-sm font-medium text-muted-foreground mb-1">Average Rating</h2>
            <div className="flex items-center gap-2">
              <span className="text-4xl font-bold">{feedbackData.averageRating}</span>
              <Star className="h-6 w-6 text-yellow-400 fill-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center h-full">
            <h2 className="text-sm font-medium text-muted-foreground mb-1">Total Reviews</h2>
            <span className="text-4xl font-bold">{feedbackData.totalReviews}</span>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center h-full">
            <h2 className="text-sm font-medium text-muted-foreground mb-1">
              Positive Reviews <span className="text-xs">(Rating 4.0+)</span>
            </h2>
            <span className="text-4xl font-bold">{feedbackData.positiveReviews}%</span>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">Top Rated Products</h2>

          <div className="space-y-6">
            {feedbackData.topProducts.map((product, index) => (
              <div key={index} className="border-b pb-4 last:border-0 last:pb-0">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                  <div>
                    <h3 className="font-medium text-lg">{product.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      {renderStars(product.rating)}
                      <span className="text-primary font-semibold">{product.rating}</span>
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Reviews: </span>
                      <span className="font-medium">{product.totalReviews}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Price Range: </span>
                      <span className="font-medium">{product.priceRange}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">In Stock: </span>
                      <span
                        className={`font-medium ${product.inStock === "Limited" ? "text-orange-500" : "text-green-600"}`}
                      >
                        {product.inStock}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold">Review List</h2>
              <p className="text-sm text-muted-foreground">Manage and respond to customer reviews</p>
            </div>
            <div className="flex flex-wrap gap-2 items-center mt-2 md:mt-0">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  className="pl-10 w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date (Newest)</SelectItem>
                  <SelectItem value="rating-high">Rating (Highest)</SelectItem>
                  <SelectItem value="rating-low">Rating (Lowest)</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="flex items-center gap-1">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Review</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReviews.map((review) => (
                  <TableRow key={review.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {review.isNew && (
                          <span className="bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full">New</span>
                        )}
                        {review.product}
                      </div>
                    </TableCell>
                    <TableCell>{review.customer}</TableCell>
                    <TableCell>{renderStars(review.rating)}</TableCell>
                    <TableCell className="max-w-xs truncate">{review.review}</TableCell>
                    <TableCell>{formatDate(review.date)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewReviewDetails(review)}>
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleRespondToReview(review)}>
                            {review.hasResponse ? "Edit Response" : "Respond to Review"}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleFlagReview(review)}>
                            Flag as Inappropriate
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredReviews.length} of {feedbackData.reviews.length} reviews
            </div>
            <div className="flex gap-1">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="bg-primary/10 text-primary">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                3
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* View Review Details Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Review Details</DialogTitle>
            <DialogDescription>Detailed information about the selected review.</DialogDescription>
          </DialogHeader>

          {selectedReview && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Product</h3>
                  <p className="text-base font-medium">{selectedReview.product}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
                  <p className="text-base font-medium">{selectedReview.customer}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Rating</h3>
                  <div className="flex items-center gap-2">
                    {renderStars(selectedReview.rating)}
                    <span className="text-base font-medium">{selectedReview.rating}/5</span>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                  <p className="text-base font-medium">{formatDate(selectedReview.date)}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Review</h3>
                <p className="text-base border p-3 rounded-md bg-gray-50">{selectedReview.review}</p>
              </div>

              {selectedReview.hasResponse && selectedReview.response && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Your Response</h3>
                  <p className="text-base border p-3 rounded-md bg-primary/5">{selectedReview.response.response}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Last updated: {formatDate(selectedReview.response.updatedAt)}
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                setIsViewDialogOpen(false)
                if (selectedReview) {
                  handleRespondToReview(selectedReview)
                }
              }}
            >
              {selectedReview?.hasResponse ? "Edit Response" : "Respond"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Respond to Review Dialog */}
      <Dialog open={isResponseDialogOpen} onOpenChange={setIsResponseDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedReview?.hasResponse ? "Edit Response" : "Respond to Review"}</DialogTitle>
            <DialogDescription>
              {selectedReview?.hasResponse
                ? "Update your response to this customer review."
                : "Write a thoughtful response to this customer review."}
            </DialogDescription>
          </DialogHeader>

          {selectedReview && (
            <div className="space-y-4 py-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Customer Review</h3>
                <div className="flex items-center gap-2 mb-2">
                  {renderStars(selectedReview.rating)}
                  <span className="text-sm font-medium">{selectedReview.rating}/5</span>
                </div>
                <p className="text-base border p-3 rounded-md bg-gray-50">{selectedReview.review}</p>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Your Response</h3>
                <Textarea
                  placeholder="Write your response here..."
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  rows={5}
                />
                <p className="text-xs text-muted-foreground">
                  Be professional, helpful, and address the customer's specific feedback.
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsResponseDialogOpen(false)
                setSelectedReview(null)
                setResponseText("")
              }}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button onClick={handleSubmitResponse} disabled={isSubmitting || !responseText.trim()}>
              {isSubmitting ? "Submitting..." : "Submit Response"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
