"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { auth } from "@/lib/firebase"

export function WithdrawFunds() {
  const [amount, setAmount] = useState("")
  const [withdrawalMethod, setWithdrawalMethod] = useState("BankTransfer")
  const [isLoading, setIsLoading] = useState(false)
  const [availableBalance, setAvailableBalance] = useState(0)
  const { toast } = useToast()

  const handleWithdrawalRequest = async () => {
    try {
      setIsLoading(true)

      // Validate amount
      const withdrawalAmount = Number.parseFloat(amount)
      if (isNaN(withdrawalAmount) || withdrawalAmount <= 0) {
        toast({
          title: "Invalid amount",
          description: "Please enter a valid withdrawal amount",
          variant: "destructive",
        })
        return
      }

      if (withdrawalAmount > availableBalance) {
        toast({
          title: "Insufficient balance",
          description: "Withdrawal amount exceeds available balance",
          variant: "destructive",
        })
        return
      }

      const user = auth.currentUser
      if (!user) {
        toast({
          title: "Authentication error",
          description: "Please log in to request a withdrawal",
          variant: "destructive",
        })
        return
      }

      const token = await user.getIdToken()

      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/payments/withdraw`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          amount: withdrawalAmount,
          withdrawalMethod,
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "Withdrawal request submitted",
          description: "Your withdrawal request has been submitted successfully",
          variant: "default",
        })

        // Reset form
        setAmount("")

        // Update available balance
        setAvailableBalance(data.remainingBalance)
      } else {
        throw new Error(data.error || "Failed to submit withdrawal request")
      }
    } catch (error) {
      console.error("Error submitting withdrawal request:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit withdrawal request",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Withdraw Funds</CardTitle>
        <CardDescription>Request a withdrawal of your available balance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount">Withdrawal Amount</Label>
            <Input
              id="amount"
              placeholder="Enter amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">Available balance: ₹{availableBalance.toLocaleString()}</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="method">Withdrawal Method</Label>
            <Select value={withdrawalMethod} onValueChange={setWithdrawalMethod} disabled={isLoading}>
              <SelectTrigger id="method">
                <SelectValue placeholder="Select method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BankTransfer">Bank Transfer</SelectItem>
                <SelectItem value="UPI">UPI</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleWithdrawalRequest} disabled={isLoading || !amount} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            "Request Withdrawal"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
