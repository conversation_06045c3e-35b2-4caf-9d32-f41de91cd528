"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { PaymentsOverview } from "./overview"
import { TransactionHistory } from "./transaction-history"
import { PaymentsSkeleton } from "./loading-skeleton"

export default function PaymentsPage() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
        fetchPaymentsData()
      }
    })
    return () => unsubscribe()
  }, [router])

  const fetchPaymentsData = async () => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching payments data:", error)
      toast({
        title: "Failed to fetch payments data",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <PaymentsSkeleton />
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Payments</h1>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transaction-history">Transaction History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <PaymentsOverview />
        </TabsContent>

        <TabsContent value="transaction-history">
          <TransactionHistory />
        </TabsContent>
      </Tabs>
    </div>
  )
}
