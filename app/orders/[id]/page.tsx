"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { OrderDetailsSkeleton } from "../loading-skeleton"
import { OrderItems } from "./components/OrderItems"
import { DeliveryInfo } from "./components/DeliveryInfo"
import { OrderTimeline } from "./components/OrderTimeline"
import { OrderHeader } from "./components/OrderHeader"
import { ActionButtons } from "./components/ActionButtons"
import { getStatusBadge } from "./components/StatusBadge"
import type { OrderDetails } from "./types"

export default function OrderDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const orderId = params.id as string
  const { toast } = useToast()

  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [activeTab, setActiveTab] = useState("items")
  const [deliveryNotes, setDeliveryNotes] = useState("")

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
        const token = await user.getIdToken()
        fetchOrderDetails(token)
      }
    })
    return () => unsubscribe()
  }, [router, orderId])

  const fetchOrderDetails = async (token: string) => {
    try {
      setIsLoading(true)

      // Fetch order details from the API
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/orders/${orderId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
                  },
      })

      if (!response.ok) {
        throw new Error("Failed to fetch order details")
      }

      const data = await response.json()

      if (data.success && data.order) {
        // Create a compatible order details object
        const orderData = data.order

        // Create a formatted address string for the UI
        const addressStr = orderData.deliveryAddress
          ? `${orderData.deliveryAddress.fullName}, ${orderData.deliveryAddress.addressLine1}, ${orderData.deliveryAddress.addressLine2 || ""}, ${orderData.deliveryAddress.city}, ${orderData.deliveryAddress.state}, ${orderData.deliveryAddress.pincode}`
          : "No address provided"

        // Calculate expected delivery date string
        let deliveryDateStr = "To be scheduled"
        if (orderData.expectedDeliveryDate) {
          const deliveryDate = new Date(orderData.expectedDeliveryDate)
          deliveryDateStr = deliveryDate.toLocaleDateString("en-US", {
            weekday: "long",
            month: "long",
            day: "numeric",
            year: "numeric",
          })
        }

        // Create a compatible order details object with UI-specific fields
        const compatibleOrderDetails: OrderDetails = {
          ...orderData,
          deliveryStatus: orderData.status, // Map status to deliveryStatus for UI
          delivery: {
            address: addressStr,
            personnel: "Assigned on delivery day",
            trackingNumber: orderData.id.substring(0, 8).toUpperCase(),
            scheduledWindow: deliveryDateStr,
            notes: orderData.notes || "No special instructions",
          },
          // Always regenerate the timeline to ensure it's up-to-date with the latest status
          timeline: generateTimelineFromOrder(orderData),
        }

        setOrderDetails(compatibleOrderDetails)
        setDeliveryNotes(compatibleOrderDetails.delivery!.notes)
      } else {
        throw new Error("Invalid response format")
      }

      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching order details:", error)
      toast({
        title: "Failed to fetch order details",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  // Helper function to generate a timeline from order data
  const generateTimelineFromOrder = (order: any) => {
    // Format date and time from timestamp
    const formatDateTime = (timestamp: string | null | undefined) => {
      if (!timestamp) return { date: "Pending", time: "" }

      const date = new Date(timestamp)
      return {
        date: date.toLocaleDateString("en-US", { month: "long", day: "numeric", year: "numeric" }),
        time: date.toLocaleTimeString("en-US", { hour: "numeric", minute: "numeric", hour12: true }),
      }
    }

    // Get formatted date/time for order creation
    const orderCreated = formatDateTime(order.createdAt)

    // Build timeline based on order status timestamps
    const timeline: any = {
      orderPlaced: {
        date: orderCreated.date,
        time: orderCreated.time,
        completed: true,
      },
      paymentPending: {
        ...formatDateTime(order.paymentPendingAt),
        completed:
          !!order.paymentPendingAt ||
          ["PaymentPending", "Paid", "Processing", "Shipped", "Delivered"].includes(order.status),
      },
      paymentConfirmed: {
        ...formatDateTime(order.paidAt),
        completed: !!order.paidAt || ["Paid", "Processing", "Shipped", "Delivered"].includes(order.status),
      },
      orderProcessed: {
        ...formatDateTime(order.processingAt),
        completed: !!order.processingAt || ["Processing", "Shipped", "Delivered"].includes(order.status),
      },
      itemsPrepared: {
        ...formatDateTime(order.processingAt),
        completed: !!order.processingAt || ["Processing", "Shipped", "Delivered"].includes(order.status),
      },
      outForDelivery: {
        ...formatDateTime(order.shippedAt),
        completed: !!order.shippedAt || ["Shipped", "Delivered"].includes(order.status),
      },
      delivered: {
        ...formatDateTime(order.deliveredAt),
        completed: !!order.deliveredAt || order.status === "Delivered",
      },
    }

    // Add cancelled status if applicable
    if (order.cancelledAt || order.status === "Cancelled") {
      timeline.cancelled = {
        ...formatDateTime(order.cancelledAt),
        completed: true,
      }
    }

    // Add returned status if applicable
    if (order.returnedAt || order.status === "Returned") {
      timeline.returned = {
        ...formatDateTime(order.returnedAt),
        completed: true,
      }
    }

    return timeline
  }

  const updateOrderStatus = async (newStatus: string, expectedDeliveryDate?: string) => {
    try {
      setIsLoading(true)
      // Get the authentication token
      const token = await user.getIdToken()

      // Prepare request body
      const requestBody: any = {
        status: newStatus,
      }

      // Add expected delivery date if provided
      if (expectedDeliveryDate) {
        requestBody.expectedDeliveryDate = expectedDeliveryDate
      }

      // Update the order status
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/orders/${orderDetails!.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update order status")
      }

      // Refresh the order details
      fetchOrderDetails(token)

      toast({
        title: `Order marked as ${newStatus}`,
        description: "The order status has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating order status:", error)
      toast({
        title: "Failed to update order status",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleMarkAsDelivered = async () => {
    // Check if the order is in a valid state to be marked as delivered
    if (orderDetails?.status !== "Shipped") {
      toast({
        title: "Cannot mark as delivered",
        description: "Order must be shipped before it can be marked as delivered.",
        variant: "destructive",
      })
      return
    }

    await updateOrderStatus("Delivered")
  }

  if (isLoading || !orderDetails) {
    return <OrderDetailsSkeleton />
  }

  // Event handlers for action buttons
  const handleEditOrder = () => {
    // This could open a modal for editing order details
    toast({
      title: "Edit Order",
      description: "This feature is not yet implemented",
    })
  }

  const handleDownloadInvoice = async () => {
    try {
      const token = await user.getIdToken()

      // Instead of opening in a new tab, fetch the invoice directly with proper headers
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/orders/${orderDetails!.id}/invoice/download`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!response.ok) {
        throw new Error(`Failed to download invoice: ${response.status} ${response.statusText}`)
      }

      // Get the blob from the response
      const blob = await response.blob()

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob)

      // Create a temporary link element
      const a = document.createElement("a")
      a.href = url
      a.download = `invoice-${orderDetails!.orderNumber}.pdf`
      document.body.appendChild(a)

      // Click the link to download the file
      a.click()

      // Clean up
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Invoice downloaded",
        description: "The invoice has been downloaded successfully.",
      })
    } catch (error) {
      console.error("Error downloading invoice:", error)
      toast({
        title: "Failed to download invoice",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    }
  }

  const handlePrintInvoice = async () => {
    try {
      const token = await user.getIdToken()

      // Create a hidden iframe to load the invoice for printing
      const iframe = document.createElement("iframe")
      iframe.style.display = "none"
      document.body.appendChild(iframe)

      // Set up a load event handler to print the iframe content
      iframe.onload = () => {
        try {
          // Try to print the iframe content
          iframe.contentWindow?.print()
        } catch (printError) {
          console.error("Error printing:", printError)
          toast({
            title: "Print dialog failed",
            description: "Please try again or download the invoice instead.",
            variant: "destructive",
          })
        } finally {
          // Clean up the iframe after a delay to allow printing
          setTimeout(() => {
            document.body.removeChild(iframe)
          }, 1000)
        }
      }

      // Set the iframe source with the token in the URL
      // This approach uses a custom header to pass the token to the server
      // The server needs to be configured to handle this header
      const printUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/orders/${orderDetails!.id}/invoice/print`

      // Fetch the print content with proper authorization
      const response = await fetch(printUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to get print content: ${response.status} ${response.statusText}`)
      }

      // Get the HTML content from the response
      const htmlContent = await response.text()

      // Write the HTML content to the iframe
      iframe.srcdoc = htmlContent

      toast({
        title: "Preparing invoice for printing",
        description: "The print dialog should open shortly.",
      })
    } catch (error) {
      console.error("Error printing invoice:", error)
      toast({
        title: "Failed to print invoice",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })

      // Clean up any iframe that might have been created
      const iframes = document.querySelectorAll("iframe")
      iframes.forEach((iframe) => {
        if (iframe.style.display === "none") {
          document.body.removeChild(iframe)
        }
      })
    }
  }

  const handleCancelOrder = async () => {
    // Check if the order can be cancelled
    if (["Delivered", "Returned", "Cancelled"].includes(orderDetails!.status)) {
      toast({
        title: "Cannot cancel order",
        description: `Order in ${orderDetails!.status} status cannot be cancelled.`,
        variant: "destructive",
      })
      return
    }

    // Confirm before cancelling
    if (confirm("Are you sure you want to cancel this order? This action cannot be undone.")) {
      await updateOrderStatus("Cancelled")
    }
  }

  const handleUpdateStatus = async (newStatus: string) => {
    // For Processing and Shipped status, we need an expected delivery date
    if (["Processing", "Shipped"].includes(newStatus)) {
      // Get expected delivery date (at least tomorrow)
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const defaultDate = tomorrow.toISOString().split("T")[0]

      const deliveryDate = prompt(
        `Please enter expected delivery date (YYYY-MM-DD, minimum ${defaultDate}):`,
        defaultDate,
      )

      if (!deliveryDate) return // User cancelled

      // Validate date format and minimum date
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/
      if (!dateRegex.test(deliveryDate)) {
        toast({
          title: "Invalid date format",
          description: "Please use YYYY-MM-DD format",
          variant: "destructive",
        })
        return
      }

      const selectedDate = new Date(deliveryDate)
      if (selectedDate < tomorrow) {
        toast({
          title: "Invalid delivery date",
          description: "Delivery date must be at least tomorrow",
          variant: "destructive",
        })
        return
      }

      await updateOrderStatus(newStatus, deliveryDate)
    } else {
      await updateOrderStatus(newStatus)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <OrderHeader
        orderNumber={orderDetails.orderNumber}
        status={orderDetails.status}
        createdAt={orderDetails.createdAt}
        id={orderDetails.id}
        finalAmount={orderDetails.finalAmount}
        paymentStatus={orderDetails.paymentStatus}
        getStatusBadge={getStatusBadge}
      />

      <Tabs defaultValue="items" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="delivery">Delivery</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="mt-6">
          <OrderItems orderItems={orderDetails.orderItems} />
        </TabsContent>

        <TabsContent value="delivery" className="mt-6">
          <DeliveryInfo
            deliveryStatus={orderDetails.deliveryStatus || orderDetails.status}
            deliveryAddress={orderDetails.deliveryAddress}
            delivery={orderDetails.delivery!}
            orderId={orderDetails.id}
            onMarkAsDelivered={handleMarkAsDelivered}
            onUpdateStatus={handleUpdateStatus}
            getStatusBadge={getStatusBadge}
            currentStatus={orderDetails.status}
          />
        </TabsContent>

        <TabsContent value="timeline" className="mt-6">
          <OrderTimeline timeline={orderDetails.timeline!} />
        </TabsContent>
      </Tabs>

      <ActionButtons
        onEditOrder={handleEditOrder}
        onDownloadInvoice={handleDownloadInvoice}
        onPrintInvoice={handlePrintInvoice}
        onCancelOrder={handleCancelOrder}
      />

      {/* Status update buttons for mobile view */}
      <div className="md:hidden">
        <h3 className="text-sm font-medium mb-2">Update Order Status</h3>
        <div className="flex flex-wrap gap-2">
          {orderDetails.status === "Paid" && (
            <Button
              className="gap-2 bg-blue-600 hover:bg-blue-700"
              onClick={() => handleUpdateStatus("Processing")}
              disabled={isLoading}
            >
              Start Processing
            </Button>
          )}

          {orderDetails.status === "Processing" && (
            <Button
              className="gap-2 bg-orange-600 hover:bg-orange-700"
              onClick={() => handleUpdateStatus("Shipped")}
              disabled={isLoading}
            >
              Mark as Shipped
            </Button>
          )}

          {orderDetails.status === "Shipped" && (
            <Button
              className="gap-2 bg-green-600 hover:bg-green-700"
              onClick={handleMarkAsDelivered}
              disabled={isLoading}
            >
              Mark as Delivered
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
