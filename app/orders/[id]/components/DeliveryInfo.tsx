"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import type { DeliveryAddress, DeliveryInfo as DeliveryInfoType } from "../types"

interface DeliveryInfoProps {
  deliveryStatus: string
  deliveryAddress: DeliveryAddress
  delivery: DeliveryInfoType
  orderId: string
  onMarkAsDelivered: () => Promise<void>
  onUpdateStatus?: (status: string) => Promise<void>
  getStatusBadge: (status: string) => React.ReactNode
  currentStatus: string
}

export function DeliveryInfo({
  deliveryStatus,
  deliveryAddress,
  delivery,
  orderId,
  onMarkAsDelivered,
  onUpdateStatus,
  getStatusBadge,
  currentStatus,
}: DeliveryInfoProps) {
  const { toast } = useToast()
  const [deliveryNotes, setDeliveryNotes] = useState(delivery.notes)
  const [isSaving, setIsSaving] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const handleSaveNotes = async () => {
    try {
      setIsSaving(true)
      // Get the authentication token
      const token = localStorage.getItem("token")
      if (!token) {
        throw new Error("Authentication token not found")
      }

      // Update the order notes
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/orders/update`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          orderId,
          notes: deliveryNotes,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to update delivery notes")
      }

      toast({
        title: "Delivery notes saved",
        description: "The delivery notes have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating delivery notes:", error)
      toast({
        title: "Failed to update delivery notes",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Delivery Information</h2>
          <p className="text-sm text-muted-foreground">Shipping and delivery details</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium mb-2">Delivery Address</h3>
            <div className="flex items-start gap-2">
              <div className="mt-0.5 text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
              </div>
              <div>
                {deliveryAddress ? (
                  <>
                    <p className="font-medium">{deliveryAddress.fullName}</p>
                    <p>{deliveryAddress.addressLine1}</p>
                    {deliveryAddress.addressLine2 && <p>{deliveryAddress.addressLine2}</p>}
                    <p>
                      {deliveryAddress.city}, {deliveryAddress.state}, {deliveryAddress.pincode}
                    </p>
                    <p>Phone: {deliveryAddress.phoneNumber}</p>
                    {deliveryAddress.landmark && <p>Landmark: {deliveryAddress.landmark}</p>}
                  </>
                ) : (
                  <p>No delivery address provided</p>
                )}
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">Delivery Status</h3>
            <div>{getStatusBadge(deliveryStatus)}</div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">Delivery Personnel</h3>
            <div className="flex items-center gap-2">
              <div className="text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <p>{delivery.personnel}</p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">Tracking Number</h3>
            <div className="flex items-center gap-2">
              <div className="text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M9 17H7A5 5 0 0 1 7 7h2"></path>
                  <path d="M15 7h2a5 5 0 1 1 0 10h-2"></path>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </div>
              <p>{delivery.trackingNumber}</p>
            </div>
          </div>

          <div className="col-span-1 md:col-span-2">
            <h3 className="text-sm font-medium mb-2">Scheduled Delivery Window</h3>
            <div className="flex items-center gap-2">
              <div className="text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
              </div>
              <p>{delivery.scheduledWindow}</p>
            </div>
          </div>

          <div className="col-span-1 md:col-span-2">
            <h3 className="text-sm font-medium mb-2">Delivery Notes</h3>
            <Textarea value={deliveryNotes} onChange={(e) => setDeliveryNotes(e.target.value)} rows={4} />
            <div className="flex justify-end mt-2">
              <Button size="sm" onClick={handleSaveNotes} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Notes"}
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-6">
          {/* Status update buttons based on current status */}
          {currentStatus === "Paid" && onUpdateStatus && (
            <Button
              className="gap-2 bg-blue-600 hover:bg-blue-700"
              onClick={() => onUpdateStatus("Processing")}
              disabled={isUpdating}
            >
              Start Processing
            </Button>
          )}

          {currentStatus === "Processing" && onUpdateStatus && (
            <Button
              className="gap-2 bg-orange-600 hover:bg-orange-700"
              onClick={() => onUpdateStatus("Shipped")}
              disabled={isUpdating}
            >
              Mark as Shipped
            </Button>
          )}

          {currentStatus === "Shipped" && (
            <Button className="gap-2 bg-green-600 hover:bg-green-700" onClick={onMarkAsDelivered} disabled={isUpdating}>
              <CheckCircle className="h-4 w-4" />
              Mark as Delivered
            </Button>
          )}

          {/* Show status message if no actions available */}
          {["Delivered", "Cancelled", "Returned"].includes(currentStatus) && (
            <div className="text-sm text-muted-foreground italic">
              No further status updates available for {currentStatus} orders
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
