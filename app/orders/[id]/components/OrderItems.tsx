"use client"

import { Card, CardContent } from "@/components/ui/card"
import type { OrderItem } from "../types"

interface OrderItemsProps {
  orderItems: OrderItem[]
}

export function OrderItems({ orderItems }: OrderItemsProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold">Items Ordered</h2>
          <p className="text-sm text-muted-foreground">{orderItems.length} items in this order</p>
        </div>

        <div className="space-y-6">
          {orderItems.map((item) => (
            <div key={item.id} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                {item.product.image ? (
                  <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                    <img src={item.product.image} alt={item.product.name} className="w-full h-full object-cover" />
                  </div>
                ) : (
                  <div className="w-16 h-16 bg-gray-200 rounded-md"></div>
                )}
                <div>
                  <h3 className="font-medium">{item.product.name}</h3>
                  <p className="text-sm text-muted-foreground">ID: {item.productId.substring(0, 8)}</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                <p className="text-sm text-muted-foreground">Rental Duration: {item.duration} months</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status: {item.status}</p>
                <p className="text-sm text-muted-foreground">Price per month: ₹{item.pricePerUnit.toLocaleString()}</p>
              </div>
              <div className="text-right">
                <p className="font-semibold">₹{item.subtotal.toLocaleString()}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
