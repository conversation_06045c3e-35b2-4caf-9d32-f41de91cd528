"use client"

import { Badge } from "@/components/ui/badge"

export function getStatusBadge(status: string) {
  switch (status) {
    case "Delivered":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Delivered</Badge>
    case "In Transit":
    case "Shipped":
      return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Shipped</Badge>
    case "Processing":
      return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Processing</Badge>
    case "Pending":
    case "PaymentPending":
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Pending</Badge>
    case "Cancelled":
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>
    case "Paid":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Paid</Badge>
    case "Returned":
      return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Returned</Badge>
    case "Failed":
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Failed</Badge>
    default:
      return <Badge>{status}</Badge>
  }
}
