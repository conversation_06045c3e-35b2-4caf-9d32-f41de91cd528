"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Copy } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface OrderHeaderProps {
  orderNumber: string
  status: string
  createdAt: string
  id: string
  finalAmount: number
  paymentStatus: "Paid" | "Pending" | "Failed"
  getStatusBadge: (status: string) => React.ReactNode
}

export function OrderHeader({
  orderNumber,
  status,
  createdAt,
  id,
  finalAmount,
  paymentStatus,
  getStatusBadge,
}: OrderHeaderProps) {
  const { toast } = useToast()

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold">{orderNumber}</h1>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                navigator.clipboard.writeText(orderNumber)
                toast({ title: "Order number copied to clipboard" })
              }}
            >
              <Copy className="h-4 w-4" />
            </Button>
            {getStatusBadge(status)}
          </div>
          <div className="text-sm text-muted-foreground">Placed on {new Date(createdAt).toLocaleDateString()}</div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Order ID</h3>
            <p className="font-medium">{id}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Order Date</h3>
            <p className="font-medium">{new Date(createdAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Amount</h3>
            <p className="font-medium">₹{finalAmount.toLocaleString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Payment Status</h3>
            <div>{getStatusBadge(paymentStatus)}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
