"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Check, Clock } from "lucide-react"
import type { Timeline } from "../types"

interface OrderTimelineProps {
  timeline: Timeline
}

export function OrderTimeline({ timeline }: OrderTimelineProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Order Timeline</h2>
          <p className="text-sm text-muted-foreground">Track the progress of this order</p>
        </div>

        <div className="space-y-6">
          {/* Order Placed - Always shown */}
          <TimelineItem
            title="Order Placed"
            date={timeline.orderPlaced.date}
            time={timeline.orderPlaced.time}
            completed={timeline.orderPlaced.completed}
          />

          {/* Payment Pending */}
          <TimelineItem
            title="Payment Pending"
            date={timeline.paymentPending.date}
            time={timeline.paymentPending.time}
            completed={timeline.paymentPending.completed}
          />

          {/* Payment Confirmed */}
          <TimelineItem
            title="Payment Confirmed"
            date={timeline.paymentConfirmed.date}
            time={timeline.paymentConfirmed.time}
            completed={timeline.paymentConfirmed.completed}
          />

          {/* If order is cancelled after payment but before processing, show cancellation here */}
          {timeline.cancelled &&
          timeline.cancelled.completed &&
          timeline.paymentConfirmed.completed &&
          !timeline.orderProcessed.completed ? (
            <TimelineItem
              title="Order Cancelled"
              date={timeline.cancelled.date}
              time={timeline.cancelled.time}
              completed={timeline.cancelled.completed}
              isLast={true}
            />
          ) : (
            <>
              {/* Order Processed */}
              <TimelineItem
                title="Order Processed"
                date={timeline.orderProcessed.date}
                time={timeline.orderProcessed.time}
                completed={timeline.orderProcessed.completed}
              />

              {/* Items Prepared */}
              <TimelineItem
                title="Items Prepared"
                date={timeline.itemsPrepared.date}
                time={timeline.itemsPrepared.time}
                completed={timeline.itemsPrepared.completed}
              />

              {/* If order is cancelled after processing but before shipping, show cancellation here */}
              {timeline.cancelled &&
              timeline.cancelled.completed &&
              timeline.orderProcessed.completed &&
              !timeline.outForDelivery.completed ? (
                <TimelineItem
                  title="Order Cancelled"
                  date={timeline.cancelled.date}
                  time={timeline.cancelled.time}
                  completed={timeline.cancelled.completed}
                  isLast={true}
                />
              ) : (
                <>
                  {/* Out for Delivery */}
                  <TimelineItem
                    title="Out for Delivery"
                    date={timeline.outForDelivery.date}
                    time={timeline.outForDelivery.time}
                    completed={timeline.outForDelivery.completed}
                  />

                  {/* If order is cancelled after shipping but before delivery, show cancellation here */}
                  {timeline.cancelled &&
                  timeline.cancelled.completed &&
                  timeline.outForDelivery.completed &&
                  !timeline.delivered.completed ? (
                    <TimelineItem
                      title="Order Cancelled"
                      date={timeline.cancelled.date}
                      time={timeline.cancelled.time}
                      completed={timeline.cancelled.completed}
                      isLast={true}
                    />
                  ) : (
                    <>
                      {/* Delivered */}
                      <TimelineItem
                        title="Delivered"
                        date={timeline.delivered.date}
                        time={timeline.delivered.time}
                        completed={timeline.delivered.completed}
                        isLast={!timeline.returned?.completed}
                      />

                      {/* Returned (if applicable) */}
                      {timeline.returned && timeline.returned.completed && (
                        <TimelineItem
                          title="Order Returned"
                          date={timeline.returned.date}
                          time={timeline.returned.time}
                          completed={timeline.returned.completed}
                          isLast={true}
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface TimelineItemProps {
  title: string
  date: string
  time: string
  completed: boolean
  isLast?: boolean
}

function TimelineItem({ title, date, time, completed, isLast = false }: TimelineItemProps) {
  return (
    <div className={`relative pl-8 ${isLast ? "" : "pb-6"}`}>
      {/* Vertical line */}
      {!isLast && (
        <div
          className={`absolute left-[7px] top-4 w-0.5 h-full ${
            completed
              ? title === "Order Cancelled"
                ? "bg-red-500"
                : title === "Order Returned"
                  ? "bg-purple-500"
                  : "bg-green-500"
              : "bg-gray-300"
          }`}
        ></div>
      )}

      {/* Circle indicator */}
      <div
        className={`absolute left-0 top-0 w-4 h-4 rounded-full ${
          completed
            ? title === "Order Cancelled"
              ? "bg-red-500"
              : title === "Order Returned"
                ? "bg-purple-500"
                : "bg-green-500"
            : "bg-gray-300"
        } flex items-center justify-center z-10 border-2 border-white`}
      >
        {completed ? (
          title === "Order Cancelled" ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-white"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          ) : (
            <Check className="h-3 w-3 text-white" />
          )
        ) : (
          <Clock className="h-3 w-3 text-gray-600" />
        )}
      </div>

      {/* Content */}
      <div className="mb-2">
        <h3
          className={`font-medium ${
            completed
              ? title === "Order Cancelled"
                ? "text-red-600"
                : title === "Order Returned"
                  ? "text-purple-600"
                  : ""
              : "text-gray-500"
          }`}
        >
          {title}
        </h3>
        <p className="text-sm text-muted-foreground">{completed ? `${date} at ${time}` : "Pending"}</p>
      </div>
    </div>
  )
}
