"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Edit, Download, Printer, X } from "lucide-react"

interface ActionButtonsProps {
  onEditOrder?: () => void
  onDownloadInvoice?: () => void
  onPrintInvoice?: () => void
  onCancelOrder?: () => void
}

export function ActionButtons({ onEditOrder, onDownloadInvoice, onPrintInvoice, onCancelOrder }: ActionButtonsProps) {
  return (
    <div className="flex flex-wrap gap-2 justify-end">
      <Button variant="outline" className="gap-2" onClick={onEditOrder}>
        <Edit className="h-4 w-4" />
        Edit Order
      </Button>
      <Button variant="outline" className="gap-2" onClick={onDownloadInvoice}>
        <Download className="h-4 w-4" />
        Download Invoice
      </Button>
      <Button variant="outline" className="gap-2" onClick={onPrintInvoice}>
        <Printer className="h-4 w-4" />
        Print Invoice
      </Button>
      <Button variant="destructive" className="gap-2" onClick={onCancelOrder}>
        <X className="h-4 w-4" />
        Cancel Order
      </Button>
    </div>
  )
}
