export interface OrderItem {
  id: string
  productId: string
  quantity: number
  pricePerUnit: number
  duration: number
  subtotal: number
  status: string
  vendorId: string | null
  product: {
    id: string
    name: string
    price: number
    image: string
    deliveryFee: number
    description: string
  }
}

export interface DeliveryAddress {
  id: string
  fullName: string
  addressLine1: string
  addressLine2?: string
  pincode: string
  city: string
  state: string
  phoneNumber: string
  landmark?: string
  deliveryInstructions?: string
}

export interface DeliveryInfo {
  address: string
  personnel: string
  trackingNumber: string
  scheduledWindow: string
  notes: string
}

export interface TimelineItem {
  date: string
  time: string
  completed: boolean
}

export interface Timeline {
  orderPlaced: TimelineItem
  paymentPending: TimelineItem
  paymentConfirmed: TimelineItem
  orderProcessed: TimelineItem
  itemsPrepared: TimelineItem
  outForDelivery: TimelineItem
  delivered: TimelineItem
  cancelled?: TimelineItem
  returned?: TimelineItem
}

export interface OrderDetails {
  id: string
  orderNumber: string
  status: string
  createdAt: string
  totalAmount: number
  taxAmount: number
  deliveryFee: number
  discountAmount: number
  finalAmount: number
  paymentMethod: string
  paymentStatus: "Paid" | "Pending" | "Failed"
  paymentId: string | null
  deliveryAddressId: string
  userId: string
  notes: string | null
  orderItems: OrderItem[]
  deliveryAddress: DeliveryAddress
  // For backward compatibility with the UI
  deliveryStatus?: string
  delivery?: DeliveryInfo
  timeline?: Timeline
}
