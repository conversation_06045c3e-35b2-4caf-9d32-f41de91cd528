"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

// Create a unified loading skeleton component that can be used across the application
export function LoadingSkeleton({ type = "default", itemCount = 5 }) {
  // Common header skeleton
  const HeaderSkeleton = () => (
    <div className="flex justify-between items-center mb-6">
      <Skeleton className="h-10 w-40" />
      <div className="flex items-center gap-2">
        <Skeleton className="h-9 w-32" />
      </div>
    </div>
  )

  // Common filters skeleton
  const FiltersSkeleton = () => (
    <div className="flex flex-col md:flex-row gap-4 items-start md:items-center mb-6">
      <Skeleton className="h-10 w-full md:w-[300px]" />
      <div className="flex flex-wrap gap-2 items-center">
        <Skeleton className="h-10 w-[180px]" />
        <Skeleton className="h-10 w-[100px]" />
      </div>
    </div>
  )

  // Common table skeleton
  const TableSkeleton = ({ columns = 6, rows = 5 }) => (
    <div className="bg-white rounded-lg border p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-4">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-6 w-full" />
        ))}
      </div>

      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="grid grid-cols-1 md:grid-cols-6 gap-4 py-4 border-b last:border-0">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="h-6 w-full" />
          ))}
        </div>
      ))}

      <div className="flex items-center justify-between mt-4">
        <Skeleton className="h-5 w-40" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-8 w-24" />
        </div>
      </div>
    </div>
  )

  // Common stats cards skeleton
  const StatsCardsSkeleton = ({ count = 4 }) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-12 rounded-lg" />
              <div>
                <Skeleton className="h-8 w-16 mb-1" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
            <Skeleton className="h-4 w-12" />
          </CardContent>
        </Card>
      ))}
    </div>
  )

  // Specific skeletons for different page types
  switch (type) {
    case "inventory":
      return (
        <div className="p-6 space-y-6">
          <HeaderSkeleton />
          <StatsCardsSkeleton />
          <FiltersSkeleton />
          <TableSkeleton columns={8} rows={itemCount} />
        </div>
      )

    case "orders":
      return (
        <div className="p-6 space-y-6">
          <HeaderSkeleton />
          <FiltersSkeleton />
          <TableSkeleton columns={7} rows={itemCount} />
        </div>
      )

    case "payments":
      return (
        <div className="p-6 space-y-6">
          <HeaderSkeleton />
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="overview" disabled>
                Overview
              </TabsTrigger>
              <TabsTrigger value="transaction-history" disabled>
                Transaction History
              </TabsTrigger>
              <TabsTrigger value="return-refunds" disabled>
                Return & Refunds
              </TabsTrigger>
            </TabsList>

            <div className="space-y-6">
              <StatsCardsSkeleton count={2} />
              <Card>
                <CardContent className="p-6">
                  <Skeleton className="h-5 w-40 mb-2" />
                  <Skeleton className="h-8 w-32 mb-1" />
                  <Skeleton className="h-4 w-24 mb-4" />
                  <Skeleton className="h-[200px] w-full" />
                </CardContent>
              </Card>
              <TableSkeleton columns={5} rows={3} />
            </div>
          </Tabs>
        </div>
      )

    case "feedback":
      return (
        <div className="p-6 space-y-6">
          <HeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6 flex flex-col items-center justify-center h-full">
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-10 w-16" />
                </CardContent>
              </Card>
            ))}
          </div>
          <Card className="mb-6">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-40 mb-6" />
              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="border-b pb-4 last:border-0 last:pb-0">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                      <div>
                        <Skeleton className="h-6 w-40 mb-1" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <div className="flex flex-col md:flex-row md:items-center gap-4">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <FiltersSkeleton />
          <TableSkeleton columns={6} rows={itemCount} />
        </div>
      )

    // Default skeleton for other pages
    default:
      return (
        <div className="p-6 space-y-6">
          <HeaderSkeleton />
          <StatsCardsSkeleton />
          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-40 mb-2" />
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-40 mb-2" />
                <Skeleton className="h-[150px] w-full" />
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-40 mb-2" />
                <Skeleton className="h-[150px] w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      )
  }
}

// Export specific skeletons for backward compatibility
export function PaymentsSkeleton() {
  return <LoadingSkeleton type="payments" />
}

export function InventorySkeleton() {
  return <LoadingSkeleton type="inventory" />
}

export function DashboardSkeleton() {
  return <LoadingSkeleton type="default" />
}

export function OrdersSkeleton() {
  return <LoadingSkeleton type="orders" />
}

export function OrderDetailsSkeleton() {
  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-40" />
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-5 w-40" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i}>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-6 w-40" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="items">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="items" disabled>
            Items
          </TabsTrigger>
          <TabsTrigger value="delivery" disabled>
            Delivery
          </TabsTrigger>
          <TabsTrigger value="timeline" disabled>
            Timeline
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-40 mb-2" />
              <Skeleton className="h-4 w-60 mb-6" />

              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Skeleton className="w-16 h-16 rounded-md" />
                      <div>
                        <Skeleton className="h-5 w-32 mb-1" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <div className="text-right">
                      <Skeleton className="h-6 w-20 ml-auto" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Tabs>

      <div className="flex flex-wrap gap-2 justify-end">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-10 w-32" />
        ))}
      </div>
    </div>
  )
}

export function FeedbackSkeleton() {
  return <LoadingSkeleton type="feedback" />
}
