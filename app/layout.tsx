import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "react-hot-toast"
import { LayoutWrapper } from "../components/layout-wrapper"
import { VendorAuthProvider } from "@/contexts/auth-context"
import { ApiProvider } from "@/components/api-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Vendor Portal",
  description: "Manage your inventory and track sales",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light">
          <VendorAuthProvider>
            <ApiProvider>
              <LayoutWrapper>{children}</LayoutWrapper>
              <Toaster position="top-right" />
            </ApiProvider>
          </VendorAuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
