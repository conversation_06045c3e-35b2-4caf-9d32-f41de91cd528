"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Loader2, Mail, Phone, MessageSquare, ExternalLink } from "lucide-react"
import { Separator } from "@/components/ui/separator"

export default function SupportPage() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
        setIsLoading(false)
      }
    })
    return () => unsubscribe()
  }, [router])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    )
  }

  return (
    <div className="container py-8 px-4 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Support</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Support Contact Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Contact Support</CardTitle>
            <CardDescription>
              Need help? Our support team is here to assist you with any questions or issues.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-6 items-start">
              <div className="w-full md:w-1/3 flex justify-center">
                <div className="relative w-48 h-48 rounded-full overflow-hidden border-4 border-purple-100">
                  <Image src="/arpit.jpeg" alt="Arpit Godghate" fill style={{ objectFit: "cover" }} priority />
                </div>
              </div>

              <div className="w-full md:w-2/3">
                <h2 className="text-xl font-semibold mb-2">Arpit Godghate</h2>
                <p className="text-muted-foreground mb-4">Vendor Support Manager</p>

                <Separator className="my-4" />

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-indigo-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Mobile</p>
                      <p className="font-medium">+91 91193 84334</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-indigo-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex gap-3 mt-6">
                    <Button className="bg-indigo-600 hover:bg-indigo-700">
                      <Phone className="mr-2 h-4 w-4" />
                      Call Now
                    </Button>
                    <Button variant="outline">
                      <Mail className="mr-2 h-4 w-4" />
                      Send Email
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Links Card */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
            <CardDescription>Helpful resources for vendors</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <MessageSquare className="mr-2 h-4 w-4" />
                FAQs
              </Button>

              <Button variant="outline" className="w-full justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                Vendor Guidelines
              </Button>

              <Button variant="outline" className="w-full justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                Shipping Policy
              </Button>

              <Button variant="outline" className="w-full justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                Return Policy
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Support Hours Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Support Hours</CardTitle>
          <CardDescription>Our support team is available during the following hours</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Weekdays</h3>
              <p>Monday - Friday</p>
              <p className="text-indigo-600 font-medium">9:00 AM - 6:00 PM IST</p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Weekends</h3>
              <p>Saturday</p>
              <p className="text-indigo-600 font-medium">10:00 AM - 4:00 PM IST</p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Holidays</h3>
              <p>Sunday and National Holidays</p>
              <p className="text-indigo-600 font-medium">Closed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
