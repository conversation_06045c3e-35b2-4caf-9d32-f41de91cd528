export interface Address {
  id: string
  fullName: string
  addressLine1: string
  addressLine2?: string
  pincode: string
  city: string
  state: string
  phoneNumber: string
  landmark?: string
  deliveryInstructions?: string
  isDefault: boolean
}

export interface BankDetails {
  id: string
  accountNumber: string
  accountName: string
  bankName: string
  ifscCode: string
  upiId?: string
}

export interface KycDetails {
  documentType?: string
  documentId?: string
  documentImage?: string
  verificationStatus?: string
}

export interface VendorProfile {
  id: string
  name: string
  email: string
  mobileNumber: string
  photoUrl?: string
  isVerified: boolean
  profileComplete: number
  pincodesServed: string[]
  addresses?: Address[]
  bankDetails?: BankDetails
  kycStatus?: "unverified" | "verified" | "under_review"
  kycDetails?: KycDetails
}
