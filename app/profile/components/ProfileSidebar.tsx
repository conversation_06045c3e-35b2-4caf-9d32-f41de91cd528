"use client"

import type React from "react"

import { useRef } from "react"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Upload, User, MapPin, CreditCard, FileCheck, AlertCircle, Check } from "lucide-react"

interface ProfileSidebarProps {
  name: string
  email: string
  photoUrl?: string
  isVerified: boolean
  activeTab: string
  isUploading: boolean
  onTabChange: (tab: string) => void
  onPhotoUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export function ProfileSidebar({
  name,
  email,
  photoUrl,
  isVerified,
  activeTab,
  isUploading,
  onTabChange,
  onPhotoUpload,
}: ProfileSidebarProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  return (
    <div className="w-full md:w-80 flex-shrink-0">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center">
            <div className="relative h-32 w-32 rounded-full overflow-hidden bg-gray-100 mb-4">
              {photoUrl ? (
                <Image src={photoUrl} alt="Profile" fill className="object-cover" />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-indigo-100 text-indigo-600 text-2xl font-semibold">
                  {name?.charAt(0) || "V"}
                </div>
              )}
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-white" />
                </div>
              )}
            </div>

            <input type="file" ref={fileInputRef} className="hidden" accept="image/*" onChange={onPhotoUpload} />

            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="mb-4"
            >
              <Upload className="h-4 w-4 mr-2" />
              Change Photo
            </Button>

            <h2 className="text-xl font-semibold text-center break-words w-full">{name}</h2>
            <p className="text-gray-500 text-sm mt-1 break-words w-full text-center">{email}</p>

            {isVerified ? (
              <div className="mt-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full flex items-center">
                <Check className="h-3 w-3 mr-1" />
                Verified Vendor
              </div>
            ) : (
              <div className="mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                Pending Verification
              </div>
            )}

            <div className="w-full mt-8 space-y-1">
              <Button
                variant={activeTab === "profile" ? "default" : "ghost"}
                className="w-full justify-start font-medium"
                onClick={() => onTabChange("profile")}
              >
                <User className="h-4 w-4 mr-3" />
                Profile Information
              </Button>
              <Button
                variant={activeTab === "addresses" ? "default" : "ghost"}
                className="w-full justify-start font-medium"
                onClick={() => onTabChange("addresses")}
              >
                <MapPin className="h-4 w-4 mr-3" />
                Addresses
              </Button>
              <Button
                variant={activeTab === "banking" ? "default" : "ghost"}
                className="w-full justify-start font-medium"
                onClick={() => onTabChange("banking")}
              >
                <CreditCard className="h-4 w-4 mr-3" />
                Banking Details
              </Button>
              <Button
                variant={activeTab === "kyc" ? "default" : "ghost"}
                className="w-full justify-start font-medium"
                onClick={() => onTabChange("kyc")}
              >
                <FileCheck className="h-4 w-4 mr-3" />
                KYC Verification
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
