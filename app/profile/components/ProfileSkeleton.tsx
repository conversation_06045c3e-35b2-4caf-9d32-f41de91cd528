import React from "react"

export function ProfileSkeleton() {
  return (
    <div className="container py-8 px-4 min-h-screen animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>

      {/* Profile Header Skeleton */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="h-6 bg-gray-200 rounded w-40 mb-4"></div>
        <div className="w-full bg-gray-200 rounded-full h-2"></div>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar Skeleton */}
        <div className="w-full md:w-1/4 bg-white rounded-lg shadow-sm p-6">
          {/* Profile Photo */}
          <div className="flex flex-col items-center mb-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full mb-3"></div>
            <div className="h-5 bg-gray-200 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-40"></div>
          </div>

          {/* Navigation Tabs */}
          <div className="space-y-2">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>

        {/* Main Content Skeleton */}
        <div className="w-full md:w-3/4 bg-white rounded-lg shadow-sm p-6">
          <div className="h-7 bg-gray-200 rounded w-48 mb-6"></div>
          
          {/* Form Fields */}
          <div className="space-y-6">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 mt-8">
            <div className="h-10 bg-gray-200 rounded w-24"></div>
            <div className="h-10 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    </div>
  )
}