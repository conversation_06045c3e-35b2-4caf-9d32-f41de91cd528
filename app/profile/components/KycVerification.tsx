"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Eye, FileText, AlertCircle } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"
import type { KycDetails } from "../types"

interface KycVerificationProps {
  kycStatus?: "unverified" | "verified" | "under_review"
  kycDetails?: KycDetails
  isLoading: boolean
  isSubmitting: boolean
  onSubmit: (documentType: string, documentId: string, file: File) => void
}

export function KycVerification({ kycStatus, kycDetails, isLoading, isSubmitting, onSubmit }: KycVerificationProps) {
  const [documentType, setDocumentType] = useState("")
  const [documentId, setDocumentId] = useState("")
  const [file, setFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string>("")
  const [fileType, setFileType] = useState<'image' | 'pdf' | null>(null)
  const [fileError, setFileError] = useState<string>("")

  console.log("kyc details : ", kycDetails)

  
  const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB in bytes

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFileError("")
    
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0]
      
      // Check file size
      if (selectedFile.size > MAX_FILE_SIZE) {
        setFileError(`File size (${formatFileSize(selectedFile.size)}) exceeds the maximum limit of 5MB. Please choose a smaller file.`)
        setFile(null)
        setPreviewUrl("")
        setFileType(null)
        // Clear the input
        e.target.value = ""
        return
      }

      setFile(selectedFile)

      // Determine file type
      const isPdf = selectedFile.type === 'application/pdf'
      setFileType(isPdf ? 'pdf' : 'image')

      // Create a preview URL for the file
      const fileReader = new FileReader()
      fileReader.onload = () => {
        if (fileReader.result) {
          setPreviewUrl(fileReader.result as string)
        }
      }
      fileReader.readAsDataURL(selectedFile)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!documentType || !documentId || !file || fileError) return
    onSubmit(documentType, documentId, file)
  }

  // Helper function to determine if a document is PDF
  const isDocumentPdf = (documentUrl: string) => {
    return documentUrl?.toLowerCase().includes('.pdf') || 
           documentUrl?.includes('application/pdf') ||
           documentUrl?.toLowerCase().endsWith('.pdf') ||
           documentUrl?.toLowerCase().includes('/raw/upload')
  }

  // Render document preview based on type
  const renderDocumentPreview = (documentUrl: string, altText: string) => {
    if (isDocumentPdf(documentUrl)) {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[200px] bg-gray-50">
          <FileText className="h-16 w-16 text-indigo-400 mb-4" />
          <p className="text-sm text-gray-600 font-medium">PDF Document</p>
          <p className="text-xs text-gray-500 mt-1 text-center">Click "View Document" to open in full screen</p>
        </div>
      )
    } else {
      return <Image src={documentUrl} alt={altText} fill className="object-contain" />
    }
  }

  // Render dialog content based on document type
  const renderDialogContent = (documentUrl: string, altText: string) => {
    if (isDocumentPdf(documentUrl)) {
      return (
        <>
          <VisuallyHidden>
            <DialogTitle>PDF Document Viewer</DialogTitle>
            <DialogDescription>View and interact with the PDF document</DialogDescription>
          </VisuallyHidden>
          <div className="w-full h-[85vh] flex flex-col">
            <div className="flex justify-between items-center mb-4 p-2 bg-gray-50 rounded-md">
              <h3 className="text-lg font-medium text-gray-900">PDF Document Viewer</h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(documentUrl, '_blank')}
                  className="flex items-center"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Open in New Tab
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = documentUrl
                    link.download = altText || 'document.pdf'
                    link.click()
                  }}
                  className="flex items-center"
                >
                  <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download
                </Button>
              </div>
            </div>
            <div className="flex-1 border border-gray-200 rounded-md overflow-hidden bg-white">
              <object
                data={documentUrl}
                type="application/pdf"
                className="w-full h-full"
                aria-label={altText}
              >
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <FileText className="h-16 w-16 text-gray-400 mb-4" />
                  <p className="text-lg font-medium text-gray-700 mb-2">PDF Preview Not Available</p>
                  <p className="text-sm text-gray-500 mb-4">Your browser doesn't support PDF preview</p>
                  <Button
                    onClick={() => window.open(documentUrl, '_blank')}
                    className="flex items-center"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Open PDF in New Tab
                  </Button>
                </div>
              </object>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              If the PDF doesn't load properly, try opening it in a new tab or downloading it.
            </div>
          </div>
        </>
      )
    } else {
      return (
        <>
          <VisuallyHidden>
            <DialogTitle>Document Image Viewer</DialogTitle>
            <DialogDescription>View the document image in full size</DialogDescription>
          </VisuallyHidden>
          <div className="w-full">
            <div className="flex justify-between items-center mb-4 p-2 bg-gray-50 rounded-md">
              <h3 className="text-lg font-medium text-gray-900">Document Image</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(documentUrl, '_blank')}
                className="flex items-center"
              >
                <Eye className="h-4 w-4 mr-1" />
                Open in New Tab
              </Button>
            </div>
            <div className="relative h-[70vh] w-full border border-gray-200 rounded-md overflow-hidden">
              <Image src={documentUrl} alt={altText} fill className="object-contain" />
            </div>
          </div>
        </>
      )
    }
  }

  // Render file upload preview
  const renderUploadPreview = () => {
    if (!previewUrl) return null

    if (fileType === 'pdf') {
      return (
        <div className="flex flex-col items-center justify-center h-40 w-full mb-2 bg-blue-50 rounded-md">
          <FileText className="h-12 w-12 text-indigo-500 mb-2" />
          <p className="text-sm text-gray-700 font-medium">PDF Document Selected</p>
          <p className="text-xs text-gray-600 text-center px-2">{file?.name}</p>
          <p className="text-xs text-gray-500 mt-1">{file && formatFileSize(file.size)}</p>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => window.open(previewUrl, '_blank')}
            className="mt-2 text-xs flex items-center"
          >
            <Eye className="h-3 w-3 mr-1" />
            Preview PDF
          </Button>
        </div>
      )
    } else {
      return (
        <div className="space-y-2">
          <div className="relative h-40 w-full border border-gray-200 rounded-md overflow-hidden">
            <Image src={previewUrl} alt="Document Preview" fill className="object-contain" />
          </div>
          <p className="text-xs text-gray-600 text-center">{file?.name}</p>
          <p className="text-xs text-gray-500 text-center">{file && formatFileSize(file.size)}</p>
        </div>
      )
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>KYC Verification</CardTitle>
          <CardDescription>Submit your documents for verification</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>KYC Verification</CardTitle>
        <CardDescription>Submit your documents for verification</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">KYC Status:</span>
            <span
              className={
                kycStatus === "verified"
                  ? "text-green-600"
                  : kycStatus === "under_review"
                    ? "text-yellow-600"
                    : "text-red-600"
              }
            >
              {kycStatus === "verified" ? "Verified" : kycStatus === "under_review" ? "Under Review" : "Unverified"}
            </span>
          </div>
        </div>

        {kycStatus === "verified" ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Document Type:</span>
              <span>{kycDetails?.documentType}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Document ID:</span>
              <span>{kycDetails?.documentId}</span>
            </div>
            {kycDetails?.documentImage && (
              <div className="mt-4">
                <div className="mb-4 border border-gray-200 rounded-md overflow-hidden">
                  <div className="relative h-48 w-full">
                    {renderDocumentPreview(kycDetails.documentImage, "KYC Document")}
                  </div>
                </div>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" />
                      {isDocumentPdf(kycDetails.documentImage) ? 'Open PDF Viewer' : 'View Document'}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden">
                    {renderDialogContent(kycDetails.documentImage, "KYC Document")}
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>
        ) : kycStatus === "under_review" ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Document Type:</span>
              <span>{kycDetails?.documentType}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Document ID:</span>
              <span>{kycDetails?.documentId}</span>
            </div>
            {kycDetails?.documentImage && (
              <div className="mt-4">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" />
                      {isDocumentPdf(kycDetails.documentImage) ? 'Open PDF Viewer' : 'View Document'}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden">
                    {renderDialogContent(kycDetails.documentImage, "KYC Document")}
                  </DialogContent>
                </Dialog>
              </div>
            )}
            <div className="mt-4 p-3 bg-yellow-50 text-yellow-800 rounded-md text-sm">
              Your documents are being reviewed. This process typically takes 1-2 business days.
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="documentType" className="block text-sm font-medium text-gray-700 mb-1">
                Document Type
              </label>
              <select
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="">Select Document Type</option>
                <option value="Aadhaar Card">Aadhaar Card</option>
                <option value="PAN Card">PAN Card</option>
                <option value="GST Certificate">GST Certificate</option>
                <option value="Business License">Business License</option>
              </select>
            </div>

            <div>
              <label htmlFor="documentId" className="block text-sm font-medium text-gray-700 mb-1">
                Document ID
              </label>
              <Input
                id="documentId"
                type="text"
                value={documentId}
                onChange={(e) => setDocumentId(e.target.value)}
                placeholder="Enter document number"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Upload Document</label>
              <div
                className={`border border-dashed ${fileError ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-md p-6 flex flex-col items-center justify-center text-gray-500 hover:bg-gray-50 transition-colors cursor-pointer`}
                onClick={() => document.getElementById("documentFile")?.click()}
              >
                <input
                  type="file"
                  id="documentFile"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="image/*,.pdf"
                />
                {previewUrl ? (
                  renderUploadPreview()
                ) : (
                  <div className="h-10 w-10 text-indigo-300 mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                )}
                <p className="text-center text-sm">
                  {previewUrl ? "Click to change document" : "Drop a picture of the document here"}
                </p>
                <p className="text-xs text-gray-400 mt-1">Supported formats: JPG, PNG, PDF (Max: 5MB)</p>
              </div>

              {fileError && (
                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                  <p className="text-sm text-red-700">{fileError}</p>
                </div>
              )}
            </div>

            <div className="pt-4">
              <Button
                type="submit"
                disabled={isSubmitting || !documentType || !documentId || !file || !!fileError}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Submit KYC Documents
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  )
}