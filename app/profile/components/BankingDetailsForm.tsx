"use client"

import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { useState, useEffect } from "react"
import { toast } from "react-hot-toast"
import { useVendorAuth } from "@/contexts/auth-context"
import { vendorApi } from "@/lib/api-client"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface BankingDetailsFormProps {
  initialUpiId: string
  initialBankAccountNo: string
  initialIfscCode: string
  initialBankName: string
  isLoading: boolean
  isSaving: boolean
  onBankingDetailsUpdated: () => void
  profileComplete?: number
}

export function BankingDetailsForm({
  initialUpiId,
  initialBankAccountNo,
  initialIfscCode,
  initialBankName,
  isLoading,
  isSaving: parentIsSaving,
  onBankingDetailsUpdated,
  profileComplete = 0,
}: BankingDetailsFormProps) {
  // Get auth context
  const { user } = useVendorAuth()
  // Local state for form values
  const [upiId, setUpiId] = useState(initialUpiId)
  const [bankAccountNo, setBankAccountNo] = useState(initialBankAccountNo)
  const [ifscCode, setIfscCode] = useState(initialIfscCode)
  const [bankName, setBankName] = useState(initialBankName)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [errors, setErrors] = useState({
    upiId: "",
    bankAccountNo: "",
    ifscCode: "",
    bankName: "",
  })

  // Update local state when props change
  useEffect(() => {
    setUpiId(initialUpiId)
    setBankAccountNo(initialBankAccountNo)
    setIfscCode(initialIfscCode)
    setBankName(initialBankName)
  }, [initialUpiId, initialBankAccountNo, initialIfscCode, initialBankName])

  // Update hasChanges when form values change
  useEffect(() => {
    setHasChanges(
      upiId !== initialUpiId ||
        bankAccountNo !== initialBankAccountNo ||
        ifscCode !== initialIfscCode ||
        bankName !== initialBankName,
    )
  }, [upiId, bankAccountNo, ifscCode, bankName, initialUpiId, initialBankAccountNo, initialIfscCode, initialBankName])

  // Validate form fields - ALL bank details required, UPI optional
  const validateForm = () => {
    let valid = true
    const newErrors = { upiId: "", bankAccountNo: "", ifscCode: "", bankName: "" }

    // UPI ID is optional but if provided, validate it
    if (upiId && !/^[a-zA-Z0-9.\-_]{2,49}@[a-zA-Z._]{2,49}$/.test(upiId)) {
      newErrors.upiId = "Please enter a valid UPI ID (e.g., name@upi)"
      valid = false
    }

    // Bank details are ALL required
    if (!bankAccountNo) {
      newErrors.bankAccountNo = "Bank account number is required"
      valid = false
    } else if (!/^\d{9,18}$/.test(bankAccountNo)) {
      newErrors.bankAccountNo = "Please enter a valid bank account number (9-18 digits)"
      valid = false
    }

    if (!ifscCode) {
      newErrors.ifscCode = "IFSC code is required"
      valid = false
    } else if (!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode)) {
      newErrors.ifscCode = "Please enter a valid IFSC code (e.g., SBIN0123456)"
      valid = false
    }

    if (!bankName) {
      newErrors.bankName = "Bank name is required"
      valid = false
    } else if (bankName.trim().length < 2) {
      newErrors.bankName = "Bank name must be at least 2 characters"
      valid = false
    }

    setErrors(newErrors)
    return valid
  }

  // Handle UPI ID change
  const handleUpiIdChange = (value: string) => {
    setUpiId(value)
    if (errors.upiId) {
      setErrors((prev) => ({ ...prev, upiId: "" }))
    }
  }

  // Handle Bank Account Number change
  const handleBankAccountNoChange = (value: string) => {
    // Only allow digits
    const digitsOnly = value.replace(/\D/g, "")
    setBankAccountNo(digitsOnly)
    if (errors.bankAccountNo) {
      setErrors((prev) => ({ ...prev, bankAccountNo: "" }))
    }
  }

  // Handle IFSC Code change
  const handleIfscCodeChange = (value: string) => {
    // Convert to uppercase and limit to 11 characters
    const uppercaseValue = value.toUpperCase().slice(0, 11)
    setIfscCode(uppercaseValue)
    if (errors.ifscCode) {
      setErrors((prev) => ({ ...prev, ifscCode: "" }))
    }
  }

  // Handle Bank Name change
  const handleBankNameChange = (value: string) => {
    setBankName(value)
    if (errors.bankName) {
      setErrors((prev) => ({ ...prev, bankName: "" }))
    }
  }

  // Handle save button click
  const handleSaveClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (validateForm()) {
      setShowConfirmDialog(true)
    }
  }

  // Handle confirm save
  const handleConfirmSave = async () => {
    if (!user) {
      toast.error("Authentication error. Please log in again.")
      return
    }

    try {
      setIsSaving(true)

      await vendorApi.updateProfile({
        upiId,
        bankAccountNo,
        ifscCode,
        bankName,
      })

      onBankingDetailsUpdated()

      // Show success toast with details
      toast.success(
        <div>
          <p className="font-semibold">Banking details updated successfully</p>
          <p className="text-sm">Bank: {bankName}</p>
          <p className="text-sm">
            Account: {bankAccountNo.substring(0, 4)}...{bankAccountNo.substring(bankAccountNo.length - 4)}
          </p>
          {upiId && <p className="text-sm">UPI ID: {upiId}</p>}
        </div>,
        { duration: 5000 },
      )

      setShowConfirmDialog(false)
      setHasChanges(false)
    } catch (error) {
      console.error("Error updating banking details:", error)
      toast.error("Failed to update banking details")
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Banking Details</CardTitle>
          <CardDescription>Update your payment and banking information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="relative">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Banking Details</CardTitle>
            <CardDescription>Update your payment and banking information</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {bankAccountNo && ifscCode && bankName ? (
              <div className="flex items-center text-green-600 text-sm font-medium">
                <CheckCircle className="h-4 w-4 mr-1" />
                Complete
              </div>
            ) : (
              <div className="flex items-center text-amber-600 text-sm font-medium">
                <AlertCircle className="h-4 w-4 mr-1" />
                Incomplete
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
          <div>
            <label htmlFor="upiId" className="block text-sm font-medium text-gray-700 mb-1">
              UPI ID <span className="text-sm text-gray-500">(Optional)</span>
            </label>
            <div className="relative">
              <Input
                id="upiId"
                type="text"
                value={upiId}
                onChange={(e) => handleUpiIdChange(e.target.value)}
                placeholder="yourname@upi"
                className={errors.upiId ? "border-red-500" : ""}
              />
              {!errors.upiId && upiId && /^[a-zA-Z0-9.\-_]{2,49}@[a-zA-Z._]{2,49}$/.test(upiId) ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : errors.upiId ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              ) : null}
            </div>
            {errors.upiId ? (
              <p className="text-red-500 text-xs mt-1">{errors.upiId}</p>
            ) : (
              <p className="text-xs text-gray-500 mt-1">Optional: Provide UPI ID for faster payments</p>
            )}
          </div>

          <div className="pt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Bank Account Details <span className="text-red-500">*</span>
            </h3>
          </div>

          <div>
            <label htmlFor="bankAccountNo" className="block text-sm font-medium text-gray-700 mb-1">
              Bank Account Number <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="bankAccountNo"
                type="text"
                value={bankAccountNo}
                onChange={(e) => handleBankAccountNoChange(e.target.value)}
                className={errors.bankAccountNo ? "border-red-500" : ""}
                placeholder="9-18 digits"
                required
              />
              {!errors.bankAccountNo && bankAccountNo && /^\d{9,18}$/.test(bankAccountNo) ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className={`h-4 w-4 ${errors.bankAccountNo ? "text-red-500" : "text-amber-600"}`} />
                </div>
              )}
            </div>
            {errors.bankAccountNo && <p className="text-red-500 text-xs mt-1">{errors.bankAccountNo}</p>}
          </div>

          <div>
            <label htmlFor="ifscCode" className="block text-sm font-medium text-gray-700 mb-1">
              IFSC Code <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="ifscCode"
                type="text"
                value={ifscCode}
                onChange={(e) => handleIfscCodeChange(e.target.value)}
                className={errors.ifscCode ? "border-red-500" : ""}
                placeholder="e.g., SBIN0123456"
                maxLength={11}
                required
              />
              {!errors.ifscCode && ifscCode && /^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode) ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className={`h-4 w-4 ${errors.ifscCode ? "text-red-500" : "text-amber-600"}`} />
                </div>
              )}
            </div>
            {errors.ifscCode && <p className="text-red-500 text-xs mt-1">{errors.ifscCode}</p>}
          </div>

          <div>
            <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="bankName"
                type="text"
                value={bankName}
                onChange={(e) => handleBankNameChange(e.target.value)}
                className={errors.bankName ? "border-red-500" : ""}
                placeholder="Enter bank name"
                required
              />
              {!errors.bankName && bankName && bankName.trim().length >= 2 ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className={`h-4 w-4 ${errors.bankName ? "text-red-500" : "text-amber-600"}`} />
                </div>
              )}
            </div>
            {errors.bankName && <p className="text-red-500 text-xs mt-1">{errors.bankName}</p>}
          </div>

          <div className="pt-4">
            <Button
              type="button"
              onClick={handleSaveClick}
              disabled={isSaving || !hasChanges}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Banking Details
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Banking Details Update</DialogTitle>
            <DialogDescription>Are you sure you want to update your banking information?</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {upiId !== initialUpiId && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">UPI ID:</div>
                <div className="text-sm font-medium">{upiId || "Not provided"}</div>
              </div>
            )}
            {bankAccountNo !== initialBankAccountNo && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">Account Number:</div>
                <div className="text-sm font-medium">{bankAccountNo}</div>
              </div>
            )}
            {ifscCode !== initialIfscCode && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">IFSC Code:</div>
                <div className="text-sm font-medium">{ifscCode}</div>
              </div>
            )}
            {bankName !== initialBankName && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">Bank Name:</div>
                <div className="text-sm font-medium">{bankName}</div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmSave} disabled={isSaving} className="bg-indigo-600 hover:bg-indigo-700">
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Confirm Update
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}