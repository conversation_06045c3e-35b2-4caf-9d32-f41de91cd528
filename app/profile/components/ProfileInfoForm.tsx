"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { useState, useEffect } from "react"
import { toast } from "react-hot-toast"
import { useVendorAuth } from "@/contexts/auth-context"
import { vendorApi } from "@/lib/api-client"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface ProfileInfoFormProps {
  email: string
  initialName: string
  initialMobileNumber: string
  isLoading: boolean
  isSaving: boolean
  onProfileUpdated: (name: string, mobileNumber: string) => void
  profileComplete?: number
}

export function ProfileInfoForm({
  email,
  initialName,
  initialMobileNumber,
  isLoading,
  isSaving: parentIsSaving,
  onProfileUpdated,
  profileComplete = 0,
}: ProfileInfoFormProps) {
  // Get auth context
  const { user } = useVendorAuth()
  // Local state for form values
  const [name, setName] = useState(initialName)
  const [mobileNumber, setMobileNumber] = useState(initialMobileNumber)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [errors, setErrors] = useState({
    name: "",
    mobileNumber: "",
  })

  // Update local state when props change
  useEffect(() => {
    setName(initialName)
    setMobileNumber(initialMobileNumber)
  }, [initialName, initialMobileNumber])

  // Update hasChanges when form values change
  useEffect(() => {
    setHasChanges(name !== initialName || mobileNumber !== initialMobileNumber)
  }, [name, mobileNumber, initialName, initialMobileNumber])

  // Validate form fields
  const validateForm = () => {
    let valid = true
    const newErrors = { name: "", mobileNumber: "" }

    if (!name.trim()) {
      newErrors.name = "Business name is required"
      valid = false
    }

    if (!mobileNumber.trim()) {
      newErrors.mobileNumber = "Mobile number is required"
      valid = false
    } else if (!/^[0-9]{10}$/.test(mobileNumber.trim())) {
      newErrors.mobileNumber = "Please enter a valid 10-digit mobile number"
      valid = false
    }

    setErrors(newErrors)
    return valid
  }

  // Handle name change
  const handleNameChange = (value: string) => {
    setName(value)
    if (errors.name && value.trim()) {
      setErrors((prev) => ({ ...prev, name: "" }))
    }
  }

  // Handle mobile number change
  const handleMobileNumberChange = (value: string) => {
    // Only allow digits
    const digitsOnly = value.replace(/\D/g, "")
    setMobileNumber(digitsOnly)

    if (errors.mobileNumber && digitsOnly.trim()) {
      if (/^[0-9]{10}$/.test(digitsOnly.trim())) {
        setErrors((prev) => ({ ...prev, mobileNumber: "" }))
      }
    }
  }

  // Handle save button click
  const handleSaveClick = () => {
    if (validateForm()) {
      setShowConfirmDialog(true)
    }
  }

  // Handle confirm save
  const handleConfirmSave = async () => {
    if (!user) {
      toast.error("Authentication error. Please log in again.")
      return
    }

    try {
      setIsSaving(true)

      await vendorApi.updateProfile({
        name,
        mobileNumber,
      })

      onProfileUpdated(name, mobileNumber)
      toast.success("Profile updated successfully")
      setShowConfirmDialog(false)
      setHasChanges(false)
    } catch (error) {
      console.error("Error updating profile:", error)
      toast.error("Failed to update profile")
    } finally {
      setIsSaving(false)
    }
  }
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>Update your business profile information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="relative">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>Update your business profile information</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {profileComplete >= 100 ? (
              <div className="flex items-center text-green-600 text-sm font-medium">
                <CheckCircle className="h-4 w-4 mr-1" />
                Complete
              </div>
            ) : (
              <div className="flex items-center text-amber-600 text-sm font-medium">
                <AlertCircle className="h-4 w-4 mr-1" />
                Incomplete
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <div className="relative">
              <Input id="email" type="email" value={email} disabled className="bg-gray-100" />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
          </div>

          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Business Name <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="name"
                type="text"
                value={name}
                onChange={(e) => handleNameChange(e.target.value)}
                className={errors.name ? "border-red-500" : ""}
              />
              {!errors.name && name ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className={`h-4 w-4 ${errors.name ? "text-red-500" : "text-amber-600"}`} />
                </div>
              )}
            </div>
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          <div>
            <label htmlFor="mobileNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Mobile Number <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                id="mobileNumber"
                type="tel"
                value={mobileNumber}
                onChange={(e) => handleMobileNumberChange(e.target.value)}
                className={errors.mobileNumber ? "border-red-500" : ""}
                maxLength={10}
                placeholder="10-digit mobile number"
              />
              {!errors.mobileNumber && mobileNumber && mobileNumber.length === 10 ? (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              ) : (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className={`h-4 w-4 ${errors.mobileNumber ? "text-red-500" : "text-amber-600"}`} />
                </div>
              )}
            </div>
            {errors.mobileNumber && <p className="text-red-500 text-xs mt-1">{errors.mobileNumber}</p>}
          </div>

          <div className="pt-4">
            <Button
              onClick={handleSaveClick}
              disabled={isSaving || !hasChanges}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Profile
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Profile Update</DialogTitle>
            <DialogDescription>Are you sure you want to update your profile information?</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {name !== initialName && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">Business Name:</div>
                <div className="text-sm font-medium">{name}</div>
              </div>
            )}
            {mobileNumber !== initialMobileNumber && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm text-muted-foreground">Mobile Number:</div>
                <div className="text-sm font-medium">{mobileNumber}</div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmSave} disabled={isSaving} className="bg-indigo-600 hover:bg-indigo-700">
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Confirm Update
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
