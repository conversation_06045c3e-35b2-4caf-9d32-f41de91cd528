"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Loader2, AlertCircle } from "lucide-react"
import type { Address } from "../types"
import { useState, useEffect } from "react"

interface AddressFormDialogProps {
  isOpen: boolean
  isEditing: boolean
  address: Partial<Address> | null
  isSaving: boolean
  onClose: () => void
  onAddressChange: (field: string, value: string | boolean) => void
  onPincodeSearch: (pincode: string) => void
  onSubmit: () => void
}

export function AddressFormDialog({
  isOpen,
  isEditing,
  address,
  isSaving,
  onClose,
  onAddressChange,
  onPincodeSearch,
  onSubmit,
}: AddressFormDialogProps) {
  const [errors, setErrors] = useState({
    fullName: "",
    phoneNumber: "",
    addressLine1: "",
    pincode: "",
    city: "",
    state: "",
  })

  // Validate form when address changes
  useEffect(() => {
    if (address) {
      validateForm()
    }
  }, [address])

  // Validate form fields
  const validateForm = () => {
    if (!address) return false

    const newErrors = {
      fullName: "",
      phoneNumber: "",
      addressLine1: "",
      pincode: "",
      city: "",
      state: "",
    }

    let isValid = true

    if (!address.fullName?.trim()) {
      newErrors.fullName = "Full name is required"
      isValid = false
    }

    if (!address.phoneNumber?.trim()) {
      newErrors.phoneNumber = "Mobile number is required"
      isValid = false
    } else if (!/^[0-9]{10}$/.test(address.phoneNumber.trim())) {
      newErrors.phoneNumber = "Please enter a valid 10-digit mobile number"
      isValid = false
    }

    if (!address.addressLine1?.trim()) {
      newErrors.addressLine1 = "Address is required"
      isValid = false
    }

    if (!address.pincode?.trim()) {
      newErrors.pincode = "Pincode is required"
      isValid = false
    } else if (!/^[0-9]{6}$/.test(address.pincode.trim())) {
      newErrors.pincode = "Please enter a valid 6-digit pincode"
      isValid = false
    }

    if (!address.city?.trim()) {
      newErrors.city = "City is required"
      isValid = false
    }

    if (!address.state?.trim()) {
      newErrors.state = "State is required"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Address" : "Add New Address"}</DialogTitle>
          <DialogDescription>Fill in the details for your business address</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="fullName">
              Full Name <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="fullName"
                placeholder="Full Name"
                value={address?.fullName || ""}
                onChange={(e) => onAddressChange("fullName", e.target.value)}
                className={errors.fullName ? "border-red-500" : ""}
              />
              {errors.fullName && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {errors.fullName && <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phoneNumber">
              Mobile Number <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="phoneNumber"
                placeholder="10-digit mobile number"
                value={address?.phoneNumber || ""}
                onChange={(e) => {
                  // Only allow digits
                  const digitsOnly = e.target.value.replace(/\D/g, "")
                  onAddressChange("phoneNumber", digitsOnly)
                }}
                maxLength={10}
                className={errors.phoneNumber ? "border-red-500" : ""}
              />
              {errors.phoneNumber && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {errors.phoneNumber ? (
              <p className="text-red-500 text-xs mt-1">{errors.phoneNumber}</p>
            ) : (
              <p className="text-xs text-gray-500">May be used to assist delivery</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="addressLine1">
              Flat, House no., Building, Company, Apartment <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="addressLine1"
                placeholder="Building Name, Street"
                value={address?.addressLine1 || ""}
                onChange={(e) => onAddressChange("addressLine1", e.target.value)}
                className={errors.addressLine1 ? "border-red-500" : ""}
              />
              {errors.addressLine1 && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {errors.addressLine1 && <p className="text-red-500 text-xs mt-1">{errors.addressLine1}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="addressLine2">Area, Street, Sector, Village</Label>
            <Input
              id="addressLine2"
              placeholder="Area, Street"
              value={address?.addressLine2 || ""}
              onChange={(e) => onAddressChange("addressLine2", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="landmark">Landmark (Optional)</Label>
            <Input
              id="landmark"
              placeholder="E.g. near apollo hospital"
              value={address?.landmark || ""}
              onChange={(e) => onAddressChange("landmark", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="pincode">
              Pincode <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="pincode"
                placeholder="6-digit Pincode"
                value={address?.pincode || ""}
                onChange={(e) => {
                  // Only allow digits
                  const digitsOnly = e.target.value.replace(/\D/g, "")
                  onAddressChange("pincode", digitsOnly)
                  if (digitsOnly.length === 6) {
                    onPincodeSearch(digitsOnly)
                  }
                }}
                maxLength={6}
                className={errors.pincode ? "border-red-500" : ""}
              />
              {errors.pincode && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {errors.pincode && <p className="text-red-500 text-xs mt-1">{errors.pincode}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">
                Town/City <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="city"
                  placeholder="City"
                  value={address?.city || ""}
                  onChange={(e) => onAddressChange("city", e.target.value)}
                  className={errors.city ? "border-red-500" : ""}
                />
                {errors.city && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  </div>
                )}
              </div>
              {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">
                State <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="state"
                  placeholder="State"
                  value={address?.state || ""}
                  onChange={(e) => onAddressChange("state", e.target.value)}
                  className={errors.state ? "border-red-500" : ""}
                />
                {errors.state && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  </div>
                )}
              </div>
              {errors.state && <p className="text-red-500 text-xs mt-1">{errors.state}</p>}
            </div>
          </div>

          {/* <div className="space-y-2">
            <Label htmlFor="deliveryInstructions">Delivery Instructions (Optional)</Label>
            <Input
              id="deliveryInstructions"
              placeholder="Add preferences, notes, access codes and more"
              value={address?.deliveryInstructions || ""}
              onChange={(e) => onAddressChange("deliveryInstructions", e.target.value)}
            />
          </div> */}

          {/* <div className="flex items-center space-x-2 pt-2">
            <input
              type="checkbox"
              id="isDefault"
              checked={address?.isDefault || false}
              onChange={(e) => onAddressChange("isDefault", e.target.checked)}
              className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <Label htmlFor="isDefault" className="text-sm font-normal">
              Make this my default address
            </Label>
          </div> */}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (validateForm()) {
                onSubmit()
              }
            }}
            disabled={isSaving}
            className="bg-indigo-600 hover:bg-indigo-700"
          >
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
