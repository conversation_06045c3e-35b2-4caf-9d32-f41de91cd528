"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, MapPin, Plus, Pencil, Trash2, Home, Building, CheckCircle, Check } from "lucide-react"
import type { Address } from "../types"

interface AddressManagerProps {
  addresses: Address[]
  isLoading: boolean
  onAddAddress: () => void
  onEditAddress: (address: Address) => void
  onDeleteAddress: (id: string) => void
  onSetDefaultAddress: (id: string) => void
}

export function AddressManager({
  addresses,
  isLoading,
  onAddAddress,
  onEditAddress,
  onDeleteAddress,
  onSetDefaultAddress,
}: AddressManagerProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Manage Addresses</CardTitle>
          <CardDescription>Add, edit or remove your business addresses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manage Addresses</CardTitle>
        <CardDescription>Add, edit or remove your business addresses</CardDescription>
      </CardHeader>
      <CardContent>
        {addresses && addresses.length > 0 ? (
          <div className="space-y-4">
            {addresses.map((address) => (
              <div key={address.id} className="border border-gray-200 rounded-md p-4 relative">
                <div className="flex justify-between">
                  <div className="flex items-center">
                    <div className="mr-3 text-indigo-600">
                      {address.isDefault ? <Home className="h-5 w-5" /> : <Building className="h-5 w-5" />}
                    </div>
                    <div>
                      <h3 className="font-medium flex items-center">
                        {address.addressLine1}
                        {address.isDefault && (
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full flex items-center">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Default
                          </span>
                        )}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {address.addressLine2 && `${address.addressLine2}, `}
                        {address.city}, {address.state}, {address.pincode}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditAddress(address)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <Pencil size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteAddress(address.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </Button>
                    {!address.isDefault && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onSetDefaultAddress(address.id)}
                        className="text-blue-500 hover:text-blue-700"
                        title="Set as default"
                      >
                        <Check size={16} />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900">No addresses found</h3>
            <p className="text-gray-500 mt-1">Add your first business address to get started</p>
          </div>
        )}

        <Button
          onClick={onAddAddress}
          className="mt-6 w-full bg-indigo-50 text-indigo-600 hover:bg-indigo-100 border border-dashed border-indigo-300"
        >
          <Plus size={18} className="mr-2" />
          Add New Address
        </Button>
      </CardContent>
    </Card>
  )
}
