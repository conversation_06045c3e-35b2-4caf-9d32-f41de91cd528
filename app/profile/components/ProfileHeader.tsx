import { Progress } from "@/components/ui/progress"
import { CheckCircle, User, MapPin, CreditCard, FileCheck } from "lucide-react"

interface ProfileHeaderProps {
  profileComplete: number
}

export function ProfileHeader({ profileComplete }: ProfileHeaderProps) {
  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-lg font-semibold">Profile Completion</h2>
        <span className="text-sm font-medium">{profileComplete || 0}%</span>
      </div>
      <Progress value={profileComplete || 0} className="h-2.5" />

      {/* Module completion status */}
      <div className="grid grid-cols-4 gap-2 mt-4">
        <div
          className={`p-2 rounded-md text-center text-xs ${profileComplete && profileComplete >= 25 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}`}
        >
          <div className="flex justify-center mb-1">
            {profileComplete && profileComplete >= 25 ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <User className="h-4 w-4" />
            )}
          </div>
          <span>Profile</span>
        </div>

        <div
          className={`p-2 rounded-md text-center text-xs ${profileComplete && profileComplete >= 50 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}`}
        >
          <div className="flex justify-center mb-1">
            {profileComplete && profileComplete >= 50 ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <MapPin className="h-4 w-4" />
            )}
          </div>
          <span>Address</span>
        </div>

        <div
          className={`p-2 rounded-md text-center text-xs ${profileComplete && profileComplete >= 75 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}`}
        >
          <div className="flex justify-center mb-1">
            {profileComplete && profileComplete >= 75 ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <CreditCard className="h-4 w-4" />
            )}
          </div>
          <span>Banking</span>
        </div>

        <div
          className={`p-2 rounded-md text-center text-xs ${profileComplete && profileComplete >= 100 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}`}
        >
          <div className="flex justify-center mb-1">
            {profileComplete && profileComplete >= 100 ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <FileCheck className="h-4 w-4" />
            )}
          </div>
          <span>KYC</span>
        </div>
      </div>
    </div>
  )
}
