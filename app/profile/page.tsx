"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { toast } from "react-hot-toast"
import { Loader2 } from "lucide-react"
import { useVendorAuth } from "@/contexts/auth-context"
import { ProtectedRoute } from "@/components/protected-route"

// Import types
import type { VendorProfile, Address } from "./types"
import { api, vendorApi } from "@/lib/api-client"

// Import components
import { ProfileHeader } from "./components/ProfileHeader"
import { ProfileSidebar } from "./components/ProfileSidebar"
import { ProfileInfoForm } from "./components/ProfileInfoForm"
import { AddressManager } from "./components/AddressManager"
import { BankingDetailsForm } from "./components/BankingDetailsForm"
import { KycVerification } from "./components/KycVerification"
import { AddressFormDialog } from "./components/AddressFormDialog"
import { ProfileSkeleton } from "./components/ProfileSkeleton"

export default function VendorProfilePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isLoadingProfile, setIsLoadingProfile] = useState(false)
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false)
  const [isLoadingBanking, setIsLoadingBanking] = useState(false)
  const [isLoadingKyc, setIsLoadingKyc] = useState(false)
  const [isSubmittingKyc, setIsSubmittingKyc] = useState(false)

  const [profile, setProfile] = useState<VendorProfile | null>(null)
  const [activeTab, setActiveTab] = useState("profile")

  // Address dialog state
  const [showAddressModal, setShowAddressModal] = useState(false)
  const [currentAddress, setCurrentAddress] = useState<Partial<Address> | null>(null)
  const [isEditingAddress, setIsEditingAddress] = useState(false)

  // Profile form states
  const [name, setName] = useState("")
  const [mobileNumber, setMobileNumber] = useState("")

  // Banking form states
  const [upiId, setUpiId] = useState("")
  const [bankAccountNo, setBankAccountNo] = useState("")
  const [ifscCode, setIfscCode] = useState("")
  const [bankName, setBankName] = useState("")

  // Import the useVendorAuth hook
  const { user } = useVendorAuth()

  useEffect(() => {
    if (user) {
      fetchVendorProfile()
      fetchProfileCompletion()
    } else {
      router.push("/login")
    }
  }, [user, router])

  const fetchProfileCompletion = async () => {
    try {
      const { data } = await vendorApi.getProfileCompletion()
      setProfile((prev) => (prev ? { ...prev, profileComplete: data.profileComplete } : null))
    } catch (error) {
      console.error("Error fetching profile completion:", error)
    }
  }

  const fetchVendorProfile = async () => {
    try {
      setIsLoading(true)
      setIsLoadingProfile(true)

      const { data } = await vendorApi.getProfile()
      setProfile(data.vendor)

      // Set form values
      setName(data.vendor.name || "")
      setMobileNumber(data.vendor.mobileNumber || "")

      // If we have bank details, load them
      if (data.vendor.bankDetails) {
        setUpiId(data.vendor.bankDetails.upiId || "")
        setBankAccountNo(data.vendor.bankDetails.accountNumber || "")
        setIfscCode(data.vendor.bankDetails.ifscCode || "")
        setBankName(data.vendor.bankDetails.bankName || "")
      }

      // Fetch additional data for each section
      fetchAddresses()
      fetchBankingDetails()
      fetchKycDetails()
    } catch (error) {
      console.error("Error fetching profile:", error)
      toast.error("Failed to load profile")
    } finally {
      setIsLoading(false)
      setIsLoadingProfile(false)
    }
  }

  const fetchAddresses = async () => {
    try {
      setIsLoadingAddresses(true)
      const { data } = await vendorApi.getAddresses()
      setProfile((prev) => (prev ? { ...prev, addresses: data.addresses } : null))
    } catch (error) {
      console.error("Error fetching addresses:", error)
    } finally {
      setIsLoadingAddresses(false)
    }
  }

  const fetchBankingDetails = async () => {
    try {
      setIsLoadingBanking(true)
      // Use the vendor profile endpoint to get banking details
      const { data } = await vendorApi.getProfile()

      // Update banking form states if banking details exist
      if (data.vendor.bankDetails) {
        setUpiId(data.vendor.bankDetails.upiId || "")
        setBankAccountNo(data.vendor.bankDetails.accountNumber || "")
        setIfscCode(data.vendor.bankDetails.ifscCode || "")
        setBankName(data.vendor.bankDetails.bankName || "")
      }

      // Update profile state
      setProfile((prev) => (prev ? { ...prev, bankDetails: data.vendor.bankDetails } : null))
    } catch (error) {
      console.error("Error fetching banking details:", error)
    } finally {
      setIsLoadingBanking(false)
    }
  }

  const fetchKycDetails = async () => {
    try {
      setIsLoadingKyc(true)
      const { data } = await vendorApi.getKycDetails()

      setProfile((prev) =>
        prev
          ? {
              ...prev,
              kycStatus: data.kycStatus,
              kycDetails: {
                documentType: data.documentType,
                documentId: data.documentId,
                documentImage: data.documentImage,
                verificationStatus: data.verificationStatus,
              },
            }
          : null,
      )
    } catch (error) {
      console.error("Error fetching KYC details:", error)
    } finally {
      setIsLoadingKyc(false)
    }
  }

  const handlePincodeSearch = async (pincodeValue: string) => {
    if (pincodeValue.length !== 6) return

    try {
      const { data } = await api.get(`/api/user/addresses/pincode?pincode=${pincodeValue}`)

      if (data.success && currentAddress) {
        setCurrentAddress((prev) => ({
          ...prev,
          city: data.city,
          state: data.state,
        }))
      }
    } catch (error) {
      console.error("Error fetching pincode data:", error)
    }
  }

  const handlePhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return

    const file = e.target.files[0]
    const formData = new FormData()
    formData.append("photo", file)

    try {
      setIsUploading(true)
      const { data } = await vendorApi.uploadProfilePhoto(formData)

      setProfile((prev) => (prev ? { ...prev, photoUrl: data.photoUrl } : null))
      toast.success("Profile photo updated")
    } catch (error) {
      console.error("Error uploading photo:", error)
      toast.error("Failed to upload photo")
    } finally {
      setIsUploading(false)
    }
  }

  const openAddressModal = (address?: Address) => {
    if (address) {
      setCurrentAddress(address)
      setIsEditingAddress(true)
    } else {
      setCurrentAddress({ isDefault: (profile?.addresses?.length || 0) === 0 })
      setIsEditingAddress(false)
    }
    setShowAddressModal(true)
  }

  const handleAddressChange = (field: string, value: string | boolean) => {
    setCurrentAddress((prev) => ({ ...prev, [field]: value }))
  }

  const handleAddressSubmit = async () => {
    if (
      !currentAddress ||
      !currentAddress.addressLine1 ||
      !currentAddress.pincode ||
      !currentAddress.city ||
      !currentAddress.state
    ) {
      toast.error("Please fill in all required fields")
      return
    }

    try {
      setIsSaving(true)

      if (isEditingAddress && currentAddress.id) {
        await vendorApi.updateAddress(currentAddress.id, currentAddress)
      } else {
        await vendorApi.addAddress(currentAddress)
      }

      await fetchAddresses()
      setShowAddressModal(false)
      setCurrentAddress(null)
      toast.success(isEditingAddress ? "Address updated successfully" : "Address added successfully")
      fetchProfileCompletion()
    } catch (error) {
      console.error("Error with address:", error)
      toast.error(isEditingAddress ? "Failed to update address" : "Failed to add address")
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteAddress = async (id: string) => {
    try {
      await vendorApi.deleteAddress(id)

      await fetchAddresses()
      toast.success("Address deleted successfully")
      fetchProfileCompletion()
    } catch (error) {
      console.error("Error deleting address:", error)
      toast.error("Failed to delete address")
    }
  }

  const handleSetDefaultAddress = async (id: string) => {
    try {
      await vendorApi.updateAddress(id, { isDefault: true })

      await fetchAddresses()
      toast.success("Default address updated")
    } catch (error) {
      console.error("Error setting default address:", error)
      toast.error("Failed to set default address")
    }
  }

  const handleSubmitKyc = async (documentType: string, documentId: string, file: File) => {
    try {
      setIsSubmittingKyc(true)
      const formData = new FormData()
      formData.append("documentType", documentType)
      formData.append("documentId", documentId)
      formData.append("documentImage", file)

      const { data } = await vendorApi.submitKyc(formData)

      setProfile((prev) =>
        prev
          ? {
              ...prev,
              kycStatus: data.kycStatus,
              kycDetails: {
                documentType: data.documentType,
                documentId: data.documentId,
                documentImage: data.documentImage,
              },
            }
          : null,
      )

      toast.success("KYC documents submitted successfully")
      fetchProfileCompletion()
      fetchKycDetails()
    } catch (error) {
      console.error("Error submitting KYC documents:", error)
      toast.error("Failed to submit KYC documents")
    } finally {
      setIsSubmittingKyc(false)
    }
  }

  if (isLoading) {
    return (
     <ProtectedRoute>
        <ProfileSkeleton />
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="container py-8 px-4 min-h-screen">
        <h1 className="text-2xl font-bold mb-6">Vendor Profile</h1>

        {/* Profile Completion Progress */}
        <ProfileHeader profileComplete={profile?.profileComplete || 0} />

        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <ProfileSidebar
            name={profile?.name || ""}
            email={profile?.email || ""}
            photoUrl={profile?.photoUrl}
            isVerified={profile?.isVerified || false}
            activeTab={activeTab}
            isUploading={isUploading}
            onTabChange={setActiveTab}
            onPhotoUpload={handlePhotoUpload}
          />

        {/* Main Content */}
        <div className="w-full md:w-3/4">
          {activeTab === "profile" && (
            <ProfileInfoForm
              email={profile?.email || ""}
              initialName={name}
              initialMobileNumber={mobileNumber}
              isLoading={isLoadingProfile}
              isSaving={isSaving}
              onProfileUpdated={(updatedName, updatedMobileNumber) => {
                setName(updatedName)
                setMobileNumber(updatedMobileNumber)
                fetchProfileCompletion()
              }}
              profileComplete={profile?.profileComplete || 0}

            />
          )}

          {activeTab === "addresses" && (
            <AddressManager
              addresses={profile?.addresses || []}
              isLoading={isLoadingAddresses}
              onAddAddress={() => openAddressModal()}
              onEditAddress={openAddressModal}
              onDeleteAddress={handleDeleteAddress}
              onSetDefaultAddress={handleSetDefaultAddress}
            />
          )}

          {activeTab === "banking" && (
            <BankingDetailsForm
              initialUpiId={upiId}
              initialBankAccountNo={bankAccountNo}
              initialIfscCode={ifscCode}
              initialBankName={bankName}
              isLoading={isLoadingBanking}
              isSaving={isSaving}
              onBankingDetailsUpdated={() => {
                fetchProfileCompletion()
                fetchBankingDetails()
              }}
              profileComplete={profile?.profileComplete || 0}

            />
          )}

          {activeTab === "kyc" && (
            <KycVerification
              kycStatus={profile?.kycStatus}
              kycDetails={profile?.kycDetails}
              isLoading={isLoadingKyc}
              isSubmitting={isSubmittingKyc}
              onSubmit={handleSubmitKyc}
            />
          )}
        </div>
      </div>

      {/* Address Form Dialog */}
      <AddressFormDialog
        isOpen={showAddressModal}
        isEditing={isEditingAddress}
        address={currentAddress}
        isSaving={isSaving}
        onClose={() => setShowAddressModal(false)}
        onAddressChange={handleAddressChange}
        onPincodeSearch={handlePincodeSearch}
        onSubmit={handleAddressSubmit}
      />
    </div>
    </ProtectedRoute>
  )
}
