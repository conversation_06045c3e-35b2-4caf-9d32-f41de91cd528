"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { onAuthStateChanged } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Area<PERSON>hart, BarChart } from "@/components/ui/charts"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, MoreVertical, CalendarIcon } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { DashboardSkeleton } from "@/app/dashboard/loading-skeleton"
import { format } from "date-fns"

export default function Dashboard() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [period, setPeriod] = useState<"week" | "month" | "year">("week")
  const [dateRange, setDateRange] = useState<{ start: string; end: string } | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push("/login")
      } else {
        setUser(user)
        const token = await user.getIdToken()
        fetchDashboardData(token, period)
      }
    })

    return () => unsubscribe()
  }, [router, period])

  const fetchDashboardData = async (token: string, selectedPeriod: string = period) => {
    try {
      setIsLoading(true)

      // Fetch summary data
      const summaryResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/dashboard/summary?period=${selectedPeriod}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!summaryResponse.ok) {
        throw new Error("Failed to fetch dashboard summary")
      }

      const summaryData = await summaryResponse.json()

      // Set date range from summary data
      if (summaryData.summary.startDate && summaryData.summary.endDate) {
        setDateRange({
          start: summaryData.summary.startDate,
          end: summaryData.summary.endDate,
        })
      }

      // Fetch chart data
      const chartsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/dashboard/charts?period=${selectedPeriod}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!chartsResponse.ok) {
        throw new Error("Failed to fetch dashboard charts")
      }

      const chartsData = await chartsResponse.json()

      // Fetch popular products
      const productsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/dashboard/popular-products?period=${selectedPeriod}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!productsResponse.ok) {
        throw new Error("Failed to fetch popular products")
      }

      const productsData = await productsResponse.json()

      // Combine all data
      setDashboardData({
        ...summaryData.summary,
        ...chartsData.charts,
        popularProducts: productsData.popularProducts,
      })

      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching dashboard data:", error)
      toast({
        title: "Failed to fetch dashboard data",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      })

      // Set fallback data in case of error
      setDashboardData({
        totalSales: 0,
        salesGrowth: 0,
        totalOrders: 0,
        ordersGrowth: 0,
        totalProfit: 0,
        profitGrowth: 0,
        customers: 0,
        revenue: 0,
        popularProducts: [],
        salesData: [],
        ordersData: [],
        profitData: [],
        reportsData: [],
      })

      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <DashboardSkeleton />
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back {user?.displayName || "Harshit"} 👋</p>
        </div>
        <div className="flex flex-wrap items-center gap-4">
          <div className="text-sm text-muted-foreground whitespace-nowrap">
            {dateRange
              ? `${format(new Date(dateRange.start), "MMM dd, yyyy")} - ${format(new Date(dateRange.end), "MMM dd, yyyy")}`
              : "Loading date range..."}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>Time Period</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Select Period</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup
                value={period}
                onValueChange={(value) => setPeriod(value as "week" | "month" | "year")}
              >
                <DropdownMenuRadioItem value="week">Last 7 Days</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="month">This Month</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="year">This Year</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            className="gap-2 bg-purple-600 hover:bg-purple-700"
            onClick={async () => {
              if (!user) return

              try {
                const token = await user.getIdToken()
                const response = await fetch(
                  `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/dashboard/export?period=${period}`,
                  {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  },
                )

                if (!response.ok) throw new Error("Failed to download report")

                // Create a blob from the response
                const blob = await response.blob()
                const url = window.URL.createObjectURL(blob)

                // Create a temporary link and trigger download
                const a = document.createElement("a")
                a.style.display = "none"
                a.href = url
                a.download = `dashboard-report-${period}.csv`
                document.body.appendChild(a)
                a.click()

                // Clean up
                window.URL.revokeObjectURL(url)
                document.body.removeChild(a)

                toast({
                  title: "Report downloaded successfully",
                  variant: "default",
                })
              } catch (error) {
                console.error("Error downloading report:", error)
                toast({
                  title: "Failed to download report",
                  description: error instanceof Error ? error.message : "Unknown error occurred",
                  variant: "destructive",
                })
              }
            }}
          >
            <Download className="h-4 w-4" />
            <span>Download Report</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Sales & Costs
              <span className="block text-2xl font-bold text-foreground">
                ₹{dashboardData.totalSales.toLocaleString()}
              </span>
            </CardTitle>
            <div
              className={`text-xs ${dashboardData.salesGrowth >= 0 ? "text-green-500" : "text-red-500"} flex items-center`}
            >
              <span className="font-medium">
                {dashboardData.salesGrowth >= 0 ? "↑" : "↓"} {Math.abs(dashboardData.salesGrowth).toFixed(2)}%
              </span>{" "}
              vs previous period
            </div>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={dashboardData.salesData}
              categories={["sales", "cost"]}
              index="day"
              colors={["#8b5cf6", "#000000"]}
              valueFormatter={(value) => `₹${value.toLocaleString()}`}
              showLegend={false}
              showYAxis={false}
              showXAxis={true}
              height={150}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Orders
              <span className="block text-2xl font-bold text-foreground">
                {dashboardData.totalOrders.toLocaleString()}
              </span>
            </CardTitle>
            <div
              className={`text-xs ${dashboardData.ordersGrowth >= 0 ? "text-green-500" : "text-red-500"} flex items-center`}
            >
              <span className="font-medium">
                {dashboardData.ordersGrowth >= 0 ? "↑" : "↓"} {Math.abs(dashboardData.ordersGrowth).toFixed(2)}%
              </span>{" "}
              vs previous period
            </div>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={dashboardData.ordersData}
              categories={["orders"]}
              index="day"
              colors={["#8b5cf6"]}
              valueFormatter={(value) => `${value}`}
              showLegend={false}
              showYAxis={false}
              showXAxis={true}
              height={150}
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Profit
              <span className="block text-2xl font-bold text-foreground">
                ₹{dashboardData.totalProfit.toLocaleString()}
              </span>
            </CardTitle>
            <div
              className={`text-xs ${dashboardData.profitGrowth >= 0 ? "text-green-500" : "text-red-500"} flex items-center`}
            >
              <span className="font-medium">
                {dashboardData.profitGrowth >= 0 ? "↑" : "↓"} {Math.abs(dashboardData.profitGrowth).toFixed(2)}%
              </span>{" "}
              vs previous period
            </div>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={dashboardData.profitData}
              categories={["profit"]}
              index="day"
              colors={["#8b5cf6"]}
              valueFormatter={(value) => `₹${value.toLocaleString()}`}
              showLegend={false}
              showYAxis={false}
              showXAxis={true}
              height={150}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center">
              <CardTitle className="text-sm font-medium">Recent Notifications</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pt-2">
            {user && (
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Check your latest notifications to stay updated on orders, payments, and important updates.
                </div>
                <Button
                  variant="outline"
                  className="w-full justify-center"
                  onClick={() => router.push("/notifications")}
                >
                  View All Notifications
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Reports</CardTitle>
            <div className="text-xs text-muted-foreground">
              {period === "week" ? "Last 7 Days" : period === "month" ? "This Month" : "This Year"}
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between mb-4">
              <div>
                <div className="text-2xl font-bold">{dashboardData.customers.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Customers</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{dashboardData.revenue.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Revenue</div>
              </div>
            </div>
            <BarChart
              data={dashboardData.reportsData}
              categories={["value"]}
              index="day"
              colors={["#8b5cf6"]}
              valueFormatter={(value) => `${value}%`}
              showLegend={false}
              showYAxis={true}
              showXAxis={true}
              height={200}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Popular Products</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Name</TableHead>
                  <TableHead>Product ID</TableHead>
                  <TableHead>Rent/month</TableHead>
                  <TableHead>Total Rents</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dashboardData.popularProducts.map((product: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell>{product.name}</TableCell>
                    <TableCell>{product.id}</TableCell>
                    <TableCell>₹{product.price}</TableCell>
                    <TableCell>{product.rents}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Edit Product</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="col-span-1 md:col-span-2">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/inventory?action=add")}
            >
              Add New Product
            </Button>
            <Button variant="outline" className="w-full justify-start" onClick={() => router.push("/orders")}>
              View Recent Orders
            </Button>
            <Button variant="outline" className="w-full justify-start" onClick={() => router.push("/inventory")}>
              Update Inventory
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
