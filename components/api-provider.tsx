"use client"

import { <PERSON>actN<PERSON>, useEffect } from "react"
import { useVendorAuth } from "@/contexts/auth-context"
import { setTokenGetter } from "@/lib/api-client"

interface ApiProviderProps {
  children: ReactNode
}

export function ApiProvider({ children }: ApiProviderProps) {
  const { getToken } = useVendorAuth()

  // Set up the token getter function for the API client
  useEffect(() => {
    setTokenGetter(getToken)
  }, [getToken])

  return <>{children}</>
}
