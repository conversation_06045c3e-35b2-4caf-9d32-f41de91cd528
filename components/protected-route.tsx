"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useVendorAuth } from "@/contexts/auth-context"
import { Loader2 } from "lucide-react"
import { ProfileSkeleton } from "@/app/profile/components/ProfileSkeleton"

interface ProtectedRouteProps {
  children: React.ReactNode
  requireCompleteProfile?: boolean
}

export function ProtectedRoute({ 
  children, 
  requireCompleteProfile = false 
}: ProtectedRouteProps) {
  const { user, loading, isVendor, profileComplete } = useVendorAuth()
  const router = useRouter()

  useEffect(() => {
    // If not loading and no user, redirect to login
    if (!loading && !user) {
      router.push("/login")
      return
    }

    // If user exists but is not a vendor, redirect to registration
    if (!loading && user && !isVendor) {
      router.push("/register")
      return
    }

    // If requiring complete profile and profile is not complete enough, redirect to profile
    if (
      !loading && 
      user && 
      isVendor && 
      requireCompleteProfile && 
      profileComplete < 90
    ) {
      router.push("/profile")
    }
  }, [loading, user, isVendor, profileComplete, requireCompleteProfile, router])

  // Show loading state while checking authentication
  if (loading) {
    return (
      <ProfileSkeleton />
    )
  }

  // If not authenticated or doesn't meet requirements, don't render children
  if (!user || !isVendor) {
    return null
  }

  // If requiring complete profile and profile is not complete enough, don't render children
  if (requireCompleteProfile && profileComplete < 90) {
    return null
  }

  // If authenticated and meets all requirements, render children
  return <>{children}</>
}
