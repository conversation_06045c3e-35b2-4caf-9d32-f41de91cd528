"use client"

import { <PERSON><PERSON><PERSON> as Tremor<PERSON><PERSON><PERSON><PERSON> } from "@tremor/react"
import { <PERSON><PERSON><PERSON> as TremorBar<PERSON>hart } from "@tremor/react"

interface ChartProps {
  data: any[]
  categories: string[]
  index: string
  colors?: string[]
  valueFormatter?: (value: number) => string
  showLegend?: boolean
  showYAxis?: boolean
  showXAxis?: boolean
  height?: number
}

export function AreaChart({
  data,
  categories,
  index,
  colors = ["#8b5cf6"],
  valueFormatter = (value) => `${value}`,
  showLegend = true,
  showYAxis = true,
  showXAxis = true,
  height = 300,
}: ChartProps) {
  return (
    <TremorAreaChart
      data={data}
      categories={categories}
      index={index}
      colors={colors}
      valueFormatter={valueFormatter}
      showLegend={showLegend}
      showYAxis={showYAxis}
      showXAxis={showXAxis}
      className="h-full"
      style={{ height }}
    />
  )
}

export function BarChart({
  data,
  categories,
  index,
  colors = ["#8b5cf6"],
  valueFormatter = (value) => `${value}`,
  showLegend = true,
  showYAxis = true,
  showXAxis = true,
  height = 300,
}: ChartProps) {
  return (
    <TremorBarChart
      data={data}
      categories={categories}
      index={index}
      colors={colors}
      valueFormatter={valueFormatter}
      showLegend={showLegend}
      showYAxis={showYAxis}
      showXAxis={showXAxis}
      className="h-full"
      style={{ height }}
    />
  )
}
