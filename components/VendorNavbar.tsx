"use client"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { So<PERSON>, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { NotificationIcon } from "@/components/notification-icon"
import { auth } from "@/lib/firebase"

export function VendorNavbar() {
  const pathname = usePathname()

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <Link href="/dashboard" className="flex-shrink-0 flex items-center">
              <Sofa className="h-8 w-8 text-primary" />
              <span className="ml-2 text-xl font-semibold text-gray-900">L2L</span>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname === "/dashboard"
                  ? "text-primary bg-primary/10"
                  : "text-gray-700 hover:text-primary hover:bg-primary/5"
              }`}
            >
              Dashboard
            </Link>
            <Link
              href="/inventory"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname === "/inventory"
                  ? "text-primary bg-primary/10"
                  : "text-gray-700 hover:text-primary hover:bg-primary/5"
              }`}
            >
              Inventory
            </Link>
            <Link
              href="/orders"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname === "/orders"
                  ? "text-primary bg-primary/10"
                  : "text-gray-700 hover:text-primary hover:bg-primary/5"
              }`}
            >
              Orders
            </Link>
            <Link
              href="/profile"
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                pathname === "/profile"
                  ? "text-primary bg-primary/10"
                  : "text-gray-700 hover:text-primary hover:bg-primary/5"
              }`}
            >
              Profile
            </Link>
            <div className="flex items-center gap-2">
              <NotificationIcon token={auth.currentUser?.accessToken || ""} />
              <Button variant="ghost" size="icon" className="rounded-full">
                <User className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}
