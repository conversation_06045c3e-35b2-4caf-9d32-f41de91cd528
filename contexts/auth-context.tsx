"use client"

import React, { createContext, use<PERSON>ontext, useEffect, useState } from "react"
import {
  User,
  signInWithEmailAndPassword as firebaseSignInWithEmail,
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  getIdTokenResult,
  signInWithCustomToken,
} from "firebase/auth"
import { auth } from "@/lib/firebase"
import { useRouter } from "next/navigation"
import { toast } from "react-hot-toast"

// Define the shape of our authentication context
interface VendorAuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  token: string | null
  isVendor: boolean
  profileComplete: number
  signInWithGoogle: () => Promise<void>
  signInWithEmail: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  getToken: () => Promise<string | null>
  clearError: () => void
}

// Create the context with a default undefined value
const VendorAuthContext = createContext<VendorAuthContextType | undefined>(undefined)

// Provider component that wraps the app and makes auth object available
export function VendorAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isVendor, setIsVendor] = useState(false)
  const [profileComplete, setProfileComplete] = useState(0)
  const router = useRouter()

  // Function to get and refresh the token
  const refreshToken = async (currentUser: User) => {
    try {
      const newToken = await currentUser.getIdToken(true)
      setToken(newToken)
      return newToken
    } catch (error) {
      console.error("Error refreshing token:", error)
      return null
    }
  }

  // Check if the user has vendor claim
  const checkVendorClaim = async (currentUser: User) => {
    try {
      const idTokenResult = await currentUser.getIdTokenResult()
      const hasVendorClaim = !!idTokenResult.claims.vendor
      setIsVendor(hasVendorClaim)
      return hasVendorClaim
    } catch (error) {
      console.error("Error checking vendor claim:", error)
      setIsVendor(false)
      return false
    }
  }

  // Check if vendor exists in database and get profile completion
  const checkVendorExists = async (currentUser: User) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/check-exists`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ uid: currentUser.uid }),
      })

      if (!response.ok) {
        throw new Error("Failed to check vendor status")
      }

      const data = await response.json()
      setProfileComplete(data.profileComplete || 0)
      return data
    } catch (error) {
      console.error("Error checking vendor status:", error)
      return { exists: false, profileComplete: 0 }
    }
  }

  // Set up the Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // User is signed in
        setUser(user)
        const userToken = await user.getIdToken()
        setToken(userToken)

        // Check if user has vendor claim
        await checkVendorClaim(user)

        // Check if vendor exists in database
        await checkVendorExists(user)
      } else {
        // User is signed out
        setUser(null)
        setToken(null)
        setIsVendor(false)
        setProfileComplete(0)
      }
      setLoading(false)
    })

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [])

  // Sign in with Google
  const signInWithGoogle = async () => {
    setError(null)
    setLoading(true)
    try {
      const provider = new GoogleAuthProvider()
      const result = await signInWithPopup(auth, provider)
      const user = result.user

      // Check if user is already registered as a vendor
      const vendorData = await checkVendorExists(user)
      const hasVendorClaim = await checkVendorClaim(user)

      if (vendorData.exists) {
        toast.success("Logged in successfully")
        router.push(vendorData.profileComplete >= 90 ? "/dashboard" : "/profile")
      } else if (hasVendorClaim) {
        // Has vendor claim but no record in database
        router.push("/register")
      } else {
        // No vendor claim, redirect to registration
        toast.error("You are not registered as a vendor")
        router.push("/register")
      }
    } catch (error: any) {
      console.error("Google sign-in error:", error)
      setError(error.message || "Failed to sign in with Google")
      toast.error("Failed to sign in with Google")
    } finally {
      setLoading(false)
    }
  }

  // Sign in with email and password
  const signInWithEmail = async (email: string, password: string) => {
    setError(null)
    setLoading(true)
    try {
      // First, try to authenticate with our custom backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/vendor/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        const data = await response.json()
        console.log("data:", data)
        throw new Error(data.error || "Authentication failed")
      }

      const data = await response.json()

      // Sign in to Firebase with the custom token
      if (data.token) {
        await signInWithCustomToken(auth, data.token)
        toast.success("Logged in successfully")

        // Redirect based on profile completion
        router.push(data.vendor.profileComplete >= 90 ? "/dashboard" : "/profile")
      } else {
        throw new Error("No authentication token received")
      }
    } catch (error: any) {
      console.error("Email login error:", error)

      // If custom auth fails, try Firebase directly (for backward compatibility)
      try {
        const userCredential = await firebaseSignInWithEmail(auth, email, password)
        const user = userCredential.user

        // Check if user has vendor claim
        const hasVendorClaim = await checkVendorClaim(user)

        if (hasVendorClaim) {
          // Check if vendor exists in database
          const vendorData = await checkVendorExists(user)

          if (vendorData.exists) {
            toast.success("Logged in successfully")
            router.push(vendorData.profileComplete >= 90 ? "/dashboard" : "/profile")
            return
          }
        }

        // If we get here, the user doesn't have vendor access
        toast.error("You do not have vendor access")
        await firebaseSignOut(auth)
      } catch (fbError) {
        // Both custom and Firebase auth failed
        setError(error.message || "Invalid email or password")
        toast.error(error.message || "Login failed")
      }
    } finally {
      setLoading(false)
    }
  }

  // Sign out
  const signOut = async () => {
    setError(null)
    try {
      await firebaseSignOut(auth)
      toast.success("Logged out successfully")
      router.push("/login")
    } catch (error: any) {
      console.error("Sign out error:", error)
      setError(error.message || "Failed to sign out")
      toast.error("Failed to sign out")
    }
  }

  // Get the current token, refreshing if necessary
  const getToken = async (): Promise<string | null> => {
    if (!user) return null

    try {
      // Check if token is expired or will expire soon
      const idTokenResult = await getIdTokenResult(user)
      const expirationTime = new Date(idTokenResult.expirationTime).getTime()
      const now = Date.now()

      // If token expires in less than 5 minutes, refresh it
      if (expirationTime - now < 5 * 60 * 1000) {
        return refreshToken(user)
      }

      return token
    } catch (error) {
      console.error("Error getting token:", error)
      return null
    }
  }

  // Clear any authentication errors
  const clearError = () => {
    setError(null)
  }

  // Context value
  const value = {
    user,
    loading,
    error,
    token,
    isVendor,
    profileComplete,
    signInWithGoogle,
    signInWithEmail,
    signOut,
    getToken,
    clearError,
  }

  return <VendorAuthContext.Provider value={value}>{children}</VendorAuthContext.Provider>
}

// Custom hook to use the auth context
export function useVendorAuth() {
  const context = useContext(VendorAuthContext)
  if (context === undefined) {
    throw new Error("useVendorAuth must be used within a VendorAuthProvider")
  }
  return context
}
