# Build stage
FROM node:20 AS builder

WORKDIR /app

# Copy package.json and package-lock.json for dependency installation
COPY package* ./

RUN npm i --legacy-peer-deps

# Copy the rest of the application code
COPY . .

# Build the Next.js application
RUN npm run build

# Production stage
FROM node:20-slim

WORKDIR /app

# Copy necessary files from builder stage
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules

# Expose port 3002 (where your app actually runs)
EXPOSE 3002

# Set production environment
ENV NODE_ENV=production

# Add healthcheck targeting the correct port
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 CMD curl -f http://localhost:3002/ || exit 1

# Start the application
CMD ["npm", "start"]